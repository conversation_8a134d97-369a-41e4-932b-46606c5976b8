#!/bin/bash

# Linux部署包验证脚本
# 使用方法: ./verify_linux.sh

echo "🔍 验证Linux部署包..."
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查结果统计
PASS_COUNT=0
WARN_COUNT=0
FAIL_COUNT=0

# 检查函数
check_pass() {
    echo -e "${GREEN}✓ PASS${NC}: $1"
    ((PASS_COUNT++))
}

check_warn() {
    echo -e "${YELLOW}⚠ WARN${NC}: $1"
    ((WARN_COUNT++))
}

check_fail() {
    echo -e "${RED}✗ FAIL${NC}: $1"
    ((FAIL_COUNT++))
}

check_info() {
    echo -e "${BLUE}ℹ INFO${NC}: $1"
}

# 查找最新的部署包
LATEST_PACKAGE=$(ls -t dist/solve_api_*.tar.gz 2>/dev/null | head -1)

if [ -z "$LATEST_PACKAGE" ]; then
    check_fail "未找到部署包，请先运行 ./package.sh"
    exit 1
fi

check_info "验证部署包: $LATEST_PACKAGE"

# 创建临时验证目录
TEMP_DIR="/tmp/verify_$(date +%s)"
mkdir -p "$TEMP_DIR"

echo ""
echo "1. 解压部署包..."
echo "--------------------------------"

# 解压到临时目录
CURRENT_DIR=$(pwd)
cd "$TEMP_DIR"
tar -xzf "$CURRENT_DIR/$LATEST_PACKAGE" 2>/dev/null
if [ $? -eq 0 ]; then
    check_pass "部署包解压成功"
else
    check_fail "部署包解压失败"
    rm -rf "$TEMP_DIR"
    exit 1
fi

# 进入解压目录
EXTRACT_DIR=$(ls -d solve_api_* 2>/dev/null | head -1)
if [ -n "$EXTRACT_DIR" ]; then
    cd "$EXTRACT_DIR"
    check_pass "进入解压目录: $EXTRACT_DIR"
else
    check_fail "未找到解压目录"
    rm -rf "$TEMP_DIR"
    exit 1
fi

echo ""
echo "2. 验证可执行文件..."
echo "--------------------------------"

# 检查可执行文件
if [ -f "solve_api" ]; then
    # 检查文件类型
    FILE_INFO=$(file solve_api)
    if echo "$FILE_INFO" | grep -q "ELF 64-bit LSB executable"; then
        check_pass "可执行文件格式正确 (Linux x64 ELF)"
    else
        check_fail "可执行文件格式不正确: $FILE_INFO"
    fi
    
    # 检查文件权限
    if [ -x "solve_api" ]; then
        check_pass "可执行文件权限正确"
    else
        check_warn "可执行文件缺少执行权限"
    fi
    
    # 检查文件大小
    FILE_SIZE=$(stat -c%s solve_api 2>/dev/null || stat -f%z solve_api 2>/dev/null)
    if [ "$FILE_SIZE" -gt 1000000 ]; then  # 大于1MB
        check_pass "可执行文件大小合理: $(echo $FILE_SIZE | awk '{print int($1/1024/1024)"MB"}')"
    else
        check_warn "可执行文件大小可能过小: $FILE_SIZE bytes"
    fi
else
    check_fail "可执行文件不存在"
fi

echo ""
echo "3. 验证配置文件..."
echo "--------------------------------"

# 检查配置文件
if [ -f "config/config.prod.yaml" ]; then
    check_pass "生产配置文件存在"
    
    # 检查配置文件内容
    if grep -q "\${" config/config.prod.yaml; then
        check_pass "配置文件使用环境变量格式"
    else
        check_warn "配置文件可能包含明文敏感信息"
    fi
else
    check_fail "生产配置文件不存在"
fi

# 检查环境变量文件
if [ -f ".env" ]; then
    check_pass "环境变量文件存在"
    
    # 检查文件权限
    PERM=$(stat -c "%a" .env 2>/dev/null || stat -f "%A" .env 2>/dev/null)
    if [ "$PERM" = "600" ]; then
        check_pass "环境变量文件权限正确 (600)"
    else
        check_warn "环境变量文件权限: $PERM (建议600)"
    fi
else
    check_fail "环境变量文件不存在"
fi

echo ""
echo "4. 验证脚本文件..."
echo "--------------------------------"

# 检查脚本文件
SCRIPTS=("deploy.sh" "security_check.sh" "check_env.sh")
for script in "${SCRIPTS[@]}"; do
    if [ -f "$script" ]; then
        if [ -x "$script" ]; then
            check_pass "脚本文件 $script 存在且可执行"
        else
            check_warn "脚本文件 $script 缺少执行权限"
        fi
    else
        check_fail "脚本文件 $script 不存在"
    fi
done

echo ""
echo "5. 验证Web文件..."
echo "--------------------------------"

# 检查Web文件
WEB_FILES=("web/admin.html" "web/api_test.html")
for file in "${WEB_FILES[@]}"; do
    if [ -f "$file" ]; then
        check_pass "Web文件 $file 存在"
    else
        check_fail "Web文件 $file 不存在"
    fi
done

echo ""
echo "6. 验证文档文件..."
echo "--------------------------------"

# 检查文档文件
DOC_FILES=("README.md" "INSTALL.md" "SECURITY_GUIDE.md" "API_DOCS.md" "VERSION")
for file in "${DOC_FILES[@]}"; do
    if [ -f "$file" ]; then
        check_pass "文档文件 $file 存在"
    else
        check_warn "文档文件 $file 不存在"
    fi
done

echo ""
echo "7. 验证目录结构..."
echo "--------------------------------"

# 检查目录结构
DIRS=("config" "web" "scripts" "logs")
for dir in "${DIRS[@]}"; do
    if [ -d "$dir" ]; then
        check_pass "目录 $dir 存在"
    else
        check_fail "目录 $dir 不存在"
    fi
done

echo ""
echo "8. 验证版本信息..."
echo "--------------------------------"

# 检查版本信息
if [ -f "VERSION" ]; then
    VERSION_INFO=$(cat VERSION)
    if echo "$VERSION_INFO" | grep -q "Linux x64"; then
        check_pass "版本信息包含Linux平台标识"
    else
        check_warn "版本信息缺少平台标识"
    fi
    
    if echo "$VERSION_INFO" | grep -q "GOOS=linux"; then
        check_pass "版本信息包含Linux编译参数"
    else
        check_warn "版本信息缺少编译参数"
    fi
    
    check_info "版本信息:"
    echo "$VERSION_INFO" | sed 's/^/    /'
else
    check_fail "版本信息文件不存在"
fi

# 清理临时目录
cd /
rm -rf "$TEMP_DIR"

echo ""
echo "=================================="
echo "🔍 Linux部署包验证完成"
echo "=================================="
echo -e "通过: ${GREEN}$PASS_COUNT${NC}"
echo -e "警告: ${YELLOW}$WARN_COUNT${NC}"
echo -e "失败: ${RED}$FAIL_COUNT${NC}"
echo ""

if [ "$FAIL_COUNT" -gt 0 ]; then
    echo -e "${RED}❌ 部署包验证失败，请检查并重新打包！${NC}"
    exit 1
elif [ "$WARN_COUNT" -gt 0 ]; then
    echo -e "${YELLOW}⚠️  部署包验证有警告，建议检查${NC}"
    exit 2
else
    echo -e "${GREEN}✅ 部署包验证全部通过！${NC}"
    echo ""
    echo "🚀 部署包已准备就绪，可以部署到Linux服务器："
    echo "1. 上传: scp $LATEST_PACKAGE user@server:/opt/"
    echo "2. 解压: tar -xzf $(basename $LATEST_PACKAGE)"
    echo "3. 配置: cd $(basename $LATEST_PACKAGE .tar.gz) && vim .env"
    echo "4. 部署: ./deploy.sh start"
    exit 0
fi
