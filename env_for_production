# 生产环境变量文件 - 重命名为 .env 并上传到服务器
# 请根据实际情况修改以下配置

# ===========================================
# 数据库配置
# ===========================================
MYSQL_PASSWORD=5e7fFn3HpPfuQ6Qx42Az

# ===========================================
# Redis配置
# ===========================================
REDIS_PASSWORD=SsYZyxSSr8uEVWKJ

# ===========================================
# 短信服务配置
# ===========================================
SMS_ACCESS_KEY_ID=LTAI5t9g26wqutn692bcCRmR
SMS_ACCESS_KEY_SECRET=******************************

# ===========================================
# AI模型API密钥 - 必须修改
# ===========================================
QWEN_API_KEY=your_qwen_api_key_here
DEEPSEEK_API_KEY=***********************************

# ===========================================
# 安全配置 - 必须修改
# ===========================================
JWT_SECRET=your_jwt_secret_32_chars_minimum_here
ADMIN_DEFAULT_PASSWORD=your_secure_admin_password

# ===========================================
# 服务配置
# ===========================================
SERVER_PORT=8080
LOG_LEVEL=warn
GIN_MODE=release

# ===========================================
# Qwen模型参数配置
# ===========================================
QWEN_TEMPERATURE=0.1
QWEN_TOP_P=0.8
QWEN_TOP_K=50
QWEN_DO_SAMPLE=true
QWEN_FREQUENCY_PENALTY=0.0
QWEN_PRESENCE_PENALTY=0.0
QWEN_SYSTEM_MESSAGE=你是一个专业的题目识别助手，请准确识别图片中的题目内容，包括题目类型、题目文本和选项。
QWEN_RESPONSE_FORMAT=json

# ===========================================
# DeepSeek模型参数配置
# ===========================================
DEEPSEEK_TEMPERATURE=0.1
DEEPSEEK_TOP_P=0.8
DEEPSEEK_TOP_K=50
DEEPSEEK_DO_SAMPLE=true
DEEPSEEK_FREQUENCY_PENALTY=0.0
DEEPSEEK_PRESENCE_PENALTY=0.0
DEEPSEEK_SYSTEM_MESSAGE=你是一个专业的题目解答助手，请根据题目内容提供准确的答案和详细的解析。
