#!/bin/bash

# 生产部署包打包脚本
# 使用方法: ./package.sh

APP_NAME="solve_api"
VERSION=$(date +"%Y%m%d_%H%M%S")
PACKAGE_NAME="${APP_NAME}_${VERSION}"
PACKAGE_DIR="./dist/${PACKAGE_NAME}"

echo "🚀 开始打包生产部署包..."
echo "包名: $PACKAGE_NAME"
echo "=================================="

# 创建打包目录
echo "📁 创建打包目录..."
rm -rf ./dist
mkdir -p "$PACKAGE_DIR"

# 编译应用（Linux版本）
echo "🔨 编译Linux可执行文件..."
GOOS=linux GOARCH=amd64 go build -ldflags="-s -w" -o "$PACKAGE_DIR/$APP_NAME" cmd/main.go
if [ $? -ne 0 ]; then
    echo "❌ 编译失败"
    exit 1
fi
echo "✅ Linux可执行文件编译完成"

# 复制配置文件
echo "📋 复制配置文件..."
mkdir -p "$PACKAGE_DIR/config"
cp config/config.prod.yaml "$PACKAGE_DIR/config/"
cp .env.production "$PACKAGE_DIR/.env"
echo "✅ 配置文件复制完成"

# 复制脚本文件
echo "📜 复制脚本文件..."
cp deploy.sh "$PACKAGE_DIR/"
cp security_check.sh "$PACKAGE_DIR/"
cp check_env.sh "$PACKAGE_DIR/"
chmod +x "$PACKAGE_DIR/deploy.sh"
chmod +x "$PACKAGE_DIR/security_check.sh"
chmod +x "$PACKAGE_DIR/check_env.sh"
echo "✅ 脚本文件复制完成"

# 复制Web文件
echo "🌐 复制Web文件..."
mkdir -p "$PACKAGE_DIR/web"
cp web/admin.html "$PACKAGE_DIR/web/"
cp web/api_test.html "$PACKAGE_DIR/web/"
echo "✅ Web文件复制完成"

# 复制脚本目录
echo "🔧 复制脚本目录..."
mkdir -p "$PACKAGE_DIR/scripts"
cp scripts/init_db.sql "$PACKAGE_DIR/scripts/" 2>/dev/null || true
cp scripts/init_models.go "$PACKAGE_DIR/scripts/" 2>/dev/null || true
cp scripts/manage_admin.go "$PACKAGE_DIR/scripts/" 2>/dev/null || true
echo "✅ 脚本目录复制完成"

# 创建日志目录
echo "📝 创建日志目录..."
mkdir -p "$PACKAGE_DIR/logs"
echo "✅ 日志目录创建完成"

# 复制文档
echo "📚 复制文档..."
cp README.md "$PACKAGE_DIR/" 2>/dev/null || true
cp SECURITY_GUIDE.md "$PACKAGE_DIR/" 2>/dev/null || true
cp API_DOCS.md "$PACKAGE_DIR/" 2>/dev/null || true
cp FRONTEND_QUICK_START.md "$PACKAGE_DIR/" 2>/dev/null || true
cp API_INTEGRATION_GUIDE.md "$PACKAGE_DIR/" 2>/dev/null || true
echo "✅ 文档复制完成"

# 创建安装说明
echo "📖 创建安装说明..."
cat > "$PACKAGE_DIR/INSTALL.md" << 'EOF'
# 🚀 生产环境部署指南

## 📋 部署步骤

### 1. 环境准备
确保服务器已安装：
- MySQL 8.0+
- Redis 7.0+
- 防火墙已配置

### 2. 上传文件
将整个部署包上传到服务器目标目录

### 3. 配置环境变量
```bash
# 编辑环境变量文件
vim .env

# 必须修改以下配置：
# - MYSQL_PASSWORD: 数据库密码
# - REDIS_PASSWORD: Redis密码
# - QWEN_API_KEY: Qwen API密钥
# - DEEPSEEK_API_KEY: DeepSeek API密钥
# - JWT_SECRET: JWT密钥
# - ADMIN_DEFAULT_PASSWORD: 管理员密码
```

### 4. 设置文件权限
```bash
chmod +x solve_api deploy.sh security_check.sh
chmod 600 .env config/config.prod.yaml
```

### 5. 安全检查
```bash
./security_check.sh
```

### 6. 启动服务
```bash
./deploy.sh start
```

### 7. 验证部署
```bash
# 检查服务状态
./deploy.sh status

# 查看日志
./deploy.sh logs

# 测试接口
curl http://localhost:8080/health
```

## 🔧 管理命令

```bash
./deploy.sh start    # 启动服务
./deploy.sh stop     # 停止服务
./deploy.sh restart  # 重启服务
./deploy.sh status   # 查看状态
./deploy.sh logs     # 查看日志
```

## 🔒 安全注意事项

1. **立即修改默认密码**
   - 管理员账号：15688515913 / admin888
   - 必须在首次登录后立即修改

2. **配置HTTPS**
   - 生产环境必须使用HTTPS
   - 配置SSL证书

3. **防火墙设置**
   - 只开放必要端口
   - 限制访问来源

4. **定期维护**
   - 定期更新系统
   - 轮换API密钥
   - 备份数据

## 📞 技术支持

- 管理后台：http://your-domain/web/admin.html
- API测试：http://your-domain/api-test
- 文档：查看其他.md文件

## 🆘 故障排除

1. **服务无法启动**
   - 检查端口是否被占用
   - 查看日志文件
   - 验证配置文件

2. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接参数
   - 检查网络连通性

3. **API调用失败**
   - 检查API密钥配置
   - 验证网络连接
   - 查看错误日志
EOF

# 创建版本信息
echo "📊 创建版本信息..."
cat > "$PACKAGE_DIR/VERSION" << EOF
应用名称: $APP_NAME
版本号: $VERSION
目标平台: Linux x64
打包时间: $(date '+%Y-%m-%d %H:%M:%S')
Go版本: $(go version)
编译参数: GOOS=linux GOARCH=amd64 -ldflags="-s -w"
EOF

# 设置文件权限
echo "🔐 设置文件权限..."
chmod +x "$PACKAGE_DIR/$APP_NAME"
chmod +x "$PACKAGE_DIR/deploy.sh"
chmod +x "$PACKAGE_DIR/security_check.sh"
chmod 600 "$PACKAGE_DIR/.env"
chmod 600 "$PACKAGE_DIR/config/config.prod.yaml"

# 创建压缩包
echo "📦 创建压缩包..."
cd ./dist
tar -czf "${PACKAGE_NAME}.tar.gz" "$PACKAGE_NAME"
cd ..

# 计算文件大小
PACKAGE_SIZE=$(du -sh "./dist/${PACKAGE_NAME}.tar.gz" | cut -f1)

echo ""
echo "🎉 打包完成！"
echo "=================================="
echo "📦 压缩包: ./dist/${PACKAGE_NAME}.tar.gz"
echo "📁 目录: ./dist/${PACKAGE_NAME}/"
echo "📊 大小: $PACKAGE_SIZE"
echo ""
echo "📋 包含文件："
echo "  ✅ 可执行文件: $APP_NAME (Linux x64)"
echo "  ✅ 配置文件: config/config.prod.yaml"
echo "  ✅ 环境变量: .env"
echo "  ✅ 部署脚本: deploy.sh"
echo "  ✅ 安全检查: security_check.sh"
echo "  ✅ 环境检查: check_env.sh"
echo "  ✅ Web界面: web/admin.html, web/api_test.html"
echo "  ✅ 文档: README.md, SECURITY_GUIDE.md, API_DOCS.md"
echo "  ✅ 安装说明: INSTALL.md"
echo "  ✅ 版本信息: VERSION"
echo ""
echo "🚀 部署命令："
echo "  1. 上传: scp ./dist/${PACKAGE_NAME}.tar.gz user@server:/path/"
echo "  2. 解压: tar -xzf ${PACKAGE_NAME}.tar.gz"
echo "  3. 配置: cd ${PACKAGE_NAME} && vim .env"
echo "  4. 部署: ./deploy.sh start"
echo ""
echo "📞 如需帮助，请查看 INSTALL.md 文件"
