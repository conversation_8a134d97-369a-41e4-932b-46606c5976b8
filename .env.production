# 生产环境变量配置
# 请根据实际情况修改以下配置

# ===========================================
# 数据库配置
# ===========================================
MYSQL_PASSWORD=5e7fFn3HpPfuQ6Qx42Az

# ===========================================
# Redis配置
# ===========================================
REDIS_PASSWORD=SsYZyxSSr8uEVWKJ

# ===========================================
# 短信服务配置（阿里云）
# ===========================================
SMS_ACCESS_KEY_ID=LTAI5t9g26wqutn692bcCRmR
SMS_ACCESS_KEY_SECRET=******************************

# ===========================================
# AI模型API密钥
# ===========================================
# Qwen API密钥（请替换为您的密钥）
QWEN_API_KEY=your_qwen_api_key_here

# DeepSeek API密钥
DEEPSEEK_API_KEY=***********************************

# ===========================================
# 安全配置
# ===========================================
# JWT密钥（请生成新的强密钥）
JWT_SECRET=your_jwt_secret_key_here

# 管理员默认密码（请修改为强密码）
ADMIN_DEFAULT_PASSWORD=your_secure_admin_password_here

# ===========================================
# 服务配置
# ===========================================
# 配置文件路径
CONFIG_FILE=config/config.prod.yaml

# 服务端口
SERVER_PORT=8080

# 日志级别
LOG_LEVEL=warn

# 运行模式
GIN_MODE=release

# ===========================================
# 可选配置
# ===========================================
# 数据库主机（如需覆盖配置文件）
# MYSQL_HOST=your_mysql_host
# MYSQL_PORT=3306
# MYSQL_USERNAME=your_username
# MYSQL_DATABASE=your_database

# Redis主机（如需覆盖配置文件）
# REDIS_HOST=your_redis_host
# REDIS_PORT=6379
# REDIS_USERNAME=your_redis_username
# REDIS_DB=0

# API服务配置
# API_TIMEOUT=30
# MAX_CONNECTIONS=200
# RATE_LIMIT=100
