# 🔒 生产环境安全部署指南

## 🚨 立即执行的安全措施

### 1. 环境变量配置

**必须在部署前完成！**

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量文件，填入真实的密钥
vim .env
```

**必须配置的环境变量：**
```bash
# 数据库密码
MYSQL_PASSWORD=your_secure_mysql_password

# Redis密码  
REDIS_PASSWORD=your_secure_redis_password

# 短信服务密钥
SMS_ACCESS_KEY_ID=your_sms_access_key_id
SMS_ACCESS_KEY_SECRET=your_sms_access_key_secret

# AI模型API密钥
QWEN_API_KEY=your_qwen_api_key
DEEPSEEK_API_KEY=your_deepseek_api_key

# JWT密钥（生成强密钥）
JWT_SECRET=$(openssl rand -base64 32)

# 管理员默认密码（必须修改）
ADMIN_DEFAULT_PASSWORD=your_very_secure_admin_password
```

### 2. 修改默认管理员密码

**⚠️ 极其重要：立即修改默认管理员密码！**

默认账号：`15688515913` / `admin888`

登录管理后台后立即修改：
1. 访问：`http://your-domain/web/admin.html`
2. 使用默认账号登录
3. 立即修改密码为强密码

### 3. 文件权限设置

```bash
# 设置配置文件权限
chmod 600 config/*.yaml
chmod 600 .env

# 设置可执行文件权限
chmod +x solve_api
chmod +x deploy.sh

# 设置日志目录权限
chmod 755 logs/
chmod 644 logs/*.log
```

## 🛡️ 安全配置检查清单

### 数据库安全
- [ ] 修改数据库默认端口
- [ ] 使用强密码
- [ ] 限制数据库访问IP
- [ ] 启用SSL连接
- [ ] 定期备份数据

### Redis安全
- [ ] 设置强密码
- [ ] 禁用危险命令
- [ ] 限制访问IP
- [ ] 启用SSL/TLS

### 应用安全
- [ ] 使用HTTPS（生产环境必须）
- [ ] 配置防火墙规则
- [ ] 启用访问日志
- [ ] 设置合理的超时时间
- [ ] 限制文件上传大小

### API安全
- [ ] 使用强API密钥
- [ ] 启用请求签名验证
- [ ] 设置合理的限流策略
- [ ] 监控异常请求

## 🔐 密码安全策略

### 密码要求
- 最少8位字符
- 必须包含大写字母、小写字母、数字、特殊字符中的至少3种
- 不能使用常见弱密码
- 定期更换（建议90天）

### API密钥管理
- 使用强随机生成的密钥
- 定期轮换密钥（建议30天）
- 不在日志中记录完整密钥
- 使用环境变量存储

## 🚫 安全防护措施

### 已启用的安全功能
- ✅ bcrypt密码加密
- ✅ API密钥认证
- ✅ 请求签名验证
- ✅ 输入参数验证
- ✅ SQL注入防护（GORM ORM）
- ✅ XSS防护
- ✅ CSRF防护
- ✅ 安全响应头
- ✅ 限流保护
- ✅ 文件上传安全检查

### 监控和日志
- ✅ 安全事件日志
- ✅ 登录失败监控
- ✅ API调用日志
- ✅ 异常访问检测

## 🌐 网络安全

### 防火墙配置
```bash
# 只开放必要端口
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw allow 8080/tcp  # 应用端口（可选）
ufw enable
```

### HTTPS配置
**生产环境必须使用HTTPS！**

使用Let's Encrypt免费证书：
```bash
# 安装certbot
sudo apt install certbot

# 获取证书
sudo certbot certonly --standalone -d your-domain.com

# 配置nginx反向代理
sudo vim /etc/nginx/sites-available/solve_api
```

## 📊 安全监控

### 日志监控
监控以下安全事件：
- 登录失败次数过多
- API密钥验证失败
- 异常请求模式
- 文件上传异常
- 数据库连接异常

### 告警设置
建议设置以下告警：
- 5分钟内登录失败超过10次
- API调用频率异常
- 服务器资源使用率过高
- 数据库连接失败

## 🔄 定期安全维护

### 每周检查
- [ ] 检查系统更新
- [ ] 查看安全日志
- [ ] 监控异常访问

### 每月检查
- [ ] 更新依赖包
- [ ] 轮换API密钥
- [ ] 备份验证
- [ ] 安全扫描

### 每季度检查
- [ ] 密码策略审查
- [ ] 权限审计
- [ ] 安全配置审查
- [ ] 渗透测试

## 🆘 安全事件响应

### 发现安全问题时
1. 立即隔离受影响系统
2. 收集相关日志和证据
3. 评估影响范围
4. 通知相关人员
5. 制定修复计划
6. 实施修复措施
7. 验证修复效果
8. 总结经验教训

### 紧急联系方式
- 系统管理员：[联系方式]
- 安全负责人：[联系方式]
- 技术支持：[联系方式]

## 📋 安全检查命令

```bash
# 检查文件权限
ls -la config/
ls -la .env

# 检查进程状态
ps aux | grep solve_api

# 检查网络连接
netstat -tlnp | grep 8080

# 检查日志
tail -f logs/app.log | grep -i "error\|fail\|security"

# 检查磁盘空间
df -h

# 检查内存使用
free -h
```

---

**⚠️ 重要提醒：**
1. 本指南必须在生产部署前完整执行
2. 定期审查和更新安全配置
3. 保持系统和依赖包的及时更新
4. 建立完善的备份和恢复机制
