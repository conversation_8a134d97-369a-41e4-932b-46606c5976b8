#!/bin/bash

# 生产环境部署脚本
# 使用方法: ./deploy.sh [start|stop|restart|status]

APP_NAME="solve_api"
CONFIG_FILE="config/config.prod.yaml"
PID_FILE="/tmp/${APP_NAME}.pid"
LOG_FILE="logs/app.log"

# 检查配置文件
check_config() {
    if [ ! -f "$CONFIG_FILE" ]; then
        echo "错误: 配置文件 $CONFIG_FILE 不存在"
        exit 1
    fi
}

# 编译应用
build() {
    echo "正在编译应用..."
    go build -o $APP_NAME cmd/main.go
    if [ $? -ne 0 ]; then
        echo "编译失败"
        exit 1
    fi
    echo "编译完成"
}

# 启动服务
start() {
    check_config
    
    if [ -f "$PID_FILE" ]; then
        PID=$(cat $PID_FILE)
        if ps -p $PID > /dev/null 2>&1; then
            echo "服务已在运行 (PID: $PID)"
            return 0
        else
            rm -f $PID_FILE
        fi
    fi
    
    build
    
    echo "正在启动服务..."
    mkdir -p logs
    
    # 使用生产配置启动服务
    CONFIG_FILE=$CONFIG_FILE nohup ./$APP_NAME > $LOG_FILE 2>&1 &
    PID=$!
    echo $PID > $PID_FILE
    
    sleep 2
    if ps -p $PID > /dev/null 2>&1; then
        echo "服务启动成功 (PID: $PID)"
        echo "日志文件: $LOG_FILE"
    else
        echo "服务启动失败"
        rm -f $PID_FILE
        exit 1
    fi
}

# 停止服务
stop() {
    if [ ! -f "$PID_FILE" ]; then
        echo "服务未运行"
        return 0
    fi
    
    PID=$(cat $PID_FILE)
    if ps -p $PID > /dev/null 2>&1; then
        echo "正在停止服务 (PID: $PID)..."
        kill $PID
        
        # 等待进程结束
        for i in {1..10}; do
            if ! ps -p $PID > /dev/null 2>&1; then
                break
            fi
            sleep 1
        done
        
        # 如果进程仍在运行，强制杀死
        if ps -p $PID > /dev/null 2>&1; then
            echo "强制停止服务..."
            kill -9 $PID
        fi
        
        rm -f $PID_FILE
        echo "服务已停止"
    else
        echo "服务未运行"
        rm -f $PID_FILE
    fi
}

# 重启服务
restart() {
    stop
    sleep 2
    start
}

# 查看状态
status() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat $PID_FILE)
        if ps -p $PID > /dev/null 2>&1; then
            echo "服务正在运行 (PID: $PID)"
            echo "配置文件: $CONFIG_FILE"
            echo "日志文件: $LOG_FILE"
        else
            echo "服务未运行 (PID文件存在但进程不存在)"
            rm -f $PID_FILE
        fi
    else
        echo "服务未运行"
    fi
}

# 查看日志
logs() {
    if [ -f "$LOG_FILE" ]; then
        tail -f $LOG_FILE
    else
        echo "日志文件不存在: $LOG_FILE"
    fi
}

# 主函数
case "$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    logs)
        logs
        ;;
    *)
        echo "使用方法: $0 {start|stop|restart|status|logs}"
        echo ""
        echo "命令说明:"
        echo "  start   - 启动服务"
        echo "  stop    - 停止服务"
        echo "  restart - 重启服务"
        echo "  status  - 查看服务状态"
        echo "  logs    - 查看实时日志"
        exit 1
        ;;
esac

exit 0
