# 生产环境配置文件 - 直接上传到服务器 config/config.yaml
server:
  port: ${SERVER_PORT:8080}
  mode: ${GIN_MODE:release}
  read_timeout: 30s
  write_timeout: 30s

database:
  host: ${MYSQL_HOST:***********}
  port: ${MYSQL_PORT:3380}
  username: ${MYSQL_USERNAME:gmdns}
  password: ${MYSQL_PASSWORD}
  database: ${MYSQL_DATABASE:gmdns}
  charset: utf8mb4
  parse_time: true
  loc: Local
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600s

redis:
  host: ${REDIS_HOST:r-bp1t323p6w8yn2cpq0pd.redis.rds.aliyuncs.com}
  port: ${REDIS_PORT:6379}
  username: ${REDIS_USERNAME:r-bp1t323p6w8yn2cpq0}
  password: ${REDIS_PASSWORD}
  db: ${REDIS_DB:0}
  pool_size: 10
  min_idle_conns: 5

sms:
  access_key_id: ${SMS_ACCESS_KEY_ID}
  access_key_secret: ${SMS_ACCESS_KEY_SECRET}
  region: cn-hangzhou
  sign_name: 智能搜题
  template_code: SMS_123456789

ai:
  qwen:
    api_key: ${QWEN_API_KEY}
    base_url: https://dashscope.aliyuncs.com/api/v1
    model: qwen-vl-plus
    timeout: 30s
    max_retries: 3
    temperature: ${QWEN_TEMPERATURE:0.1}
    top_p: ${QWEN_TOP_P:0.8}
    top_k: ${QWEN_TOP_K:50}
    do_sample: ${QWEN_DO_SAMPLE:true}
    frequency_penalty: ${QWEN_FREQUENCY_PENALTY:0.0}
    presence_penalty: ${QWEN_PRESENCE_PENALTY:0.0}
    system_message: ${QWEN_SYSTEM_MESSAGE:你是一个专业的题目识别助手，请准确识别图片中的题目内容，包括题目类型、题目文本和选项。}
    response_format: ${QWEN_RESPONSE_FORMAT:json}

  deepseek:
    api_key: ${DEEPSEEK_API_KEY}
    base_url: https://api.deepseek.com/v1
    model: deepseek-chat
    timeout: 30s
    max_retries: 3
    temperature: ${DEEPSEEK_TEMPERATURE:0.1}
    top_p: ${DEEPSEEK_TOP_P:0.8}
    top_k: ${DEEPSEEK_TOP_K:50}
    do_sample: ${DEEPSEEK_DO_SAMPLE:true}
    frequency_penalty: ${DEEPSEEK_FREQUENCY_PENALTY:0.0}
    presence_penalty: ${DEEPSEEK_PRESENCE_PENALTY:0.0}
    system_message: ${DEEPSEEK_SYSTEM_MESSAGE:你是一个专业的题目解答助手，请根据题目内容提供准确的答案和详细的解析。}

log:
  level: ${LOG_LEVEL:warn}
  file: logs/app.log
  max_size: 100
  max_backups: 5
  max_age: 30
  compress: true

security:
  jwt_secret: ${JWT_SECRET}
  password_salt: ${PASSWORD_SALT:solve_api_2024}
  admin_default_password: ${ADMIN_DEFAULT_PASSWORD}

rate_limit:
  enabled: true
  requests_per_minute: 60
  burst: 10
