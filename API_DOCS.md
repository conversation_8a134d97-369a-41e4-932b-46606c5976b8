# 📚 拍照搜题API文档

## 🎯 概述

拍照搜题API提供图像识别和题目解析服务，支持多种题型的自动识别和智能解答。

## 🔗 快速链接

- **API测试工具**: [http://localhost:8080/api-test](http://localhost:8080/api-test)
- **管理后台**: [http://localhost:8080/web/admin.html](http://localhost:8080/web/admin.html)
- **前端接入指南**: [FRONTEND_QUICK_START.md](./FRONTEND_QUICK_START.md)
- **详细接入文档**: [API_INTEGRATION_GUIDE.md](./API_INTEGRATION_GUIDE.md)

## 🚀 核心接口

### 拍照搜题

**接口地址**: `POST /api/v1/api/search`

**请求示例**:
```bash
curl -X POST "http://localhost:8080/api/v1/api/search" \
  -H "Content-Type: application/json" \
  -H "X-App-Key: your_app_key" \
  -H "X-Secret-Key: your_secret_key" \
  -d '{"image_url": "https://example.com/question.jpg"}'
```

**响应示例**:
```json
{
  "code": 200,
  "message": "搜题成功",
  "data": {
    "id": 12345,
    "question_text": "下列哪个选项是正确的？",
    "question_type": "单选题",
    "options": {
      "A": "选项A内容",
      "B": "选项B内容",
      "C": "选项C内容",
      "D": "选项D内容"
    },
    "answer": "A:选项A内容",
    "analysis": "这道题考查的是...",
    "cache_hit": true,
    "process_time": 150
  }
}
```

## 🔑 认证方式

### 方式1：请求头认证（推荐）
```http
X-App-Key: your_app_key
X-Secret-Key: your_secret_key
```

### 方式2：请求体认证
```json
{
  "image_url": "https://example.com/image.jpg",
  "app_key": "your_app_key",
  "secret_key": "your_secret_key"
}
```

## 📊 响应字段

| 字段 | 类型 | 说明 |
|------|------|------|
| id | number | 题目唯一标识 |
| question_text | string | 题目内容 |
| question_type | string | 题目类型 |
| options | object | 选项内容 |
| answer | string | 正确答案 |
| analysis | string | 题目解析 |
| cache_hit | boolean | 是否命中缓存 |
| process_time | number | 处理时间(ms) |

## ⚠️ 错误码

| 状态码 | 说明 | 处理建议 |
|--------|------|----------|
| 400 | 参数错误 | 检查请求参数 |
| 401 | 认证失败 | 检查API密钥 |
| 402 | 余额不足 | 提示充值 |
| 429 | 请求过频 | 降低频率 |
| 500 | 服务器错误 | 稍后重试 |

## 🛠️ 开发工具

### 在线测试
访问 [API测试工具](http://localhost:8080/api-test) 进行在线测试

### 获取API密钥
1. 访问 [管理后台](http://localhost:8080/web/admin.html)
2. 使用管理员账号登录（15688515913 / admin888）
3. 在应用管理中创建应用获取密钥

## 📖 开发指南

### 前端开发
- [前端快速接入指南](./FRONTEND_QUICK_START.md) - 5分钟快速上手
- [详细接入文档](./API_INTEGRATION_GUIDE.md) - 完整开发指南

### 代码示例
```javascript
// React Hook示例
const { searchQuestion, loading, error } = useQuestionSearch();

const handleSearch = async (imageUrl) => {
  const result = await searchQuestion(imageUrl);
  console.log(result);
};
```

## 🔒 安全建议

1. **API密钥安全**
   - 不要在前端代码中硬编码API密钥
   - 使用环境变量存储敏感信息
   - 定期轮换API密钥

2. **请求安全**
   - 验证图片URL的合法性
   - 实现请求重试机制
   - 添加请求超时设置

3. **错误处理**
   - 实现完善的错误处理机制
   - 为用户提供友好的错误提示
   - 记录错误日志用于调试

## 📞 技术支持

- **文档问题**: 查看详细文档或在线测试
- **接入问题**: 参考代码示例和最佳实践
- **技术支持**: 联系开发团队

---

**💡 提示**: 建议先使用在线测试工具验证接口，再进行代码集成开发。
