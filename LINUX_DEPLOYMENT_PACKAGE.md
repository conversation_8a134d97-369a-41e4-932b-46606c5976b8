# 🐧 Linux生产部署包说明

## 📦 部署包信息

**文件名**: `solve_api_20250608_163158.tar.gz`  
**大小**: 5.5MB  
**目标平台**: Linux x64 (ELF 64-bit)  
**编译方式**: 静态链接，无外部依赖  

## ✅ 验证结果

已通过完整的部署包验证：
- ✅ 25项检查全部通过
- ✅ Linux x64 ELF可执行文件
- ✅ 配置文件环境变量化
- ✅ 脚本文件权限正确
- ✅ 文档和Web界面完整

## 🚀 快速部署指南

### 1. 上传到Linux服务器
```bash
# 上传部署包
scp solve_api_20250608_163158.tar.gz user@your-server:/opt/

# 登录服务器
ssh user@your-server
```

### 2. 解压和配置
```bash
# 进入目录并解压
cd /opt
tar -xzf solve_api_20250608_163158.tar.gz
cd solve_api_20250608_163158

# 检查环境变量配置
./check_env.sh

# 编辑环境变量（根据检查结果修改）
vim .env
```

### 3. 启动服务
```bash
# 设置权限（如果需要）
chmod +x solve_api deploy.sh security_check.sh check_env.sh

# 安全检查
./security_check.sh

# 启动服务
./deploy.sh start

# 检查状态
./deploy.sh status
```

### 4. 验证部署
```bash
# 健康检查
curl http://localhost:8080/health

# 访问管理后台
curl -I http://localhost:8080/web/admin.html

# 测试API
curl -X POST http://localhost:8080/api/v1/api/search \
  -H "Content-Type: application/json" \
  -H "X-App-Key: your_app_key" \
  -H "X-Secret-Key: your_secret_key" \
  -d '{"image_url": "https://example.com/test.jpg"}'
```

## 🔧 系统要求

### 最低要求
- **操作系统**: Linux x64 (CentOS 7+, Ubuntu 16.04+, Debian 9+)
- **内存**: 512MB RAM
- **磁盘**: 100MB 可用空间
- **网络**: 可访问外部API服务

### 依赖服务
- **MySQL**: 8.0+ (远程数据库已配置)
- **Redis**: 7.0+ (远程Redis已配置)
- **网络**: 需要访问以下服务
  - 阿里云短信服务
  - Qwen API服务
  - DeepSeek API服务

### 端口要求
- **8080**: 应用服务端口（可配置）
- **出站**: 443/80 (HTTPS/HTTP API调用)

## 🔑 必须配置的环境变量

在 `.env` 文件中必须修改以下配置：

```bash
# 数据库配置
MYSQL_PASSWORD=your_mysql_password

# Redis配置
REDIS_PASSWORD=your_redis_password

# 短信服务配置
SMS_ACCESS_KEY_ID=your_sms_access_key_id
SMS_ACCESS_KEY_SECRET=your_sms_access_key_secret

# AI模型API密钥
QWEN_API_KEY=your_qwen_api_key
DEEPSEEK_API_KEY=your_deepseek_api_key

# 安全配置
JWT_SECRET=your_jwt_secret_32_chars_min
ADMIN_DEFAULT_PASSWORD=your_secure_admin_password
```

## 🛠️ 管理命令

```bash
# 服务管理
./deploy.sh start      # 启动服务
./deploy.sh stop       # 停止服务
./deploy.sh restart    # 重启服务
./deploy.sh status     # 查看状态
./deploy.sh logs       # 查看日志

# 检查工具
./check_env.sh         # 检查环境变量配置
./security_check.sh    # 安全配置检查
```

## 🌐 访问地址

服务启动后可访问：

- **健康检查**: `http://your-server:8080/health`
- **管理后台**: `http://your-server:8080/web/admin.html`
- **API测试工具**: `http://your-server:8080/api-test`
- **搜题接口**: `http://your-server:8080/api/v1/api/search`

## 🔒 安全注意事项

### 立即修改的默认配置
1. **管理员密码**: 默认 `15688515913` / `admin888`
2. **环境变量**: 所有 `your_*_here` 的占位符
3. **文件权限**: 确保 `.env` 权限为 600

### 生产环境建议
1. **启用HTTPS**: 配置SSL证书
2. **防火墙**: 只开放必要端口
3. **监控**: 设置日志监控和告警
4. **备份**: 定期备份配置和数据

## 📊 性能特性

- **静态编译**: 无外部依赖，部署简单
- **内存优化**: 运行时内存占用约50-100MB
- **并发处理**: 支持高并发API请求
- **缓存机制**: Redis缓存提升响应速度
- **限流保护**: 多层级限流防护

## 🆘 故障排除

### 常见问题

1. **权限错误**
   ```bash
   chmod +x solve_api deploy.sh security_check.sh check_env.sh
   ```

2. **端口被占用**
   ```bash
   lsof -i :8080
   # 或修改 .env 中的 SERVER_PORT
   ```

3. **配置错误**
   ```bash
   ./check_env.sh  # 检查配置
   ./security_check.sh  # 安全检查
   ```

4. **服务无法启动**
   ```bash
   ./solve_api  # 直接运行查看错误
   cat logs/app.log  # 查看日志
   ```

### 获取帮助
- 查看 `INSTALL.md` 详细安装说明
- 查看 `SECURITY_GUIDE.md` 安全配置
- 查看 `API_DOCS.md` API文档
- 使用在线测试工具调试

## 📞 技术支持

### 部署包信息
- **版本**: 20250608_163158
- **平台**: Linux x64
- **编译器**: Go 1.24.0
- **编译参数**: `GOOS=linux GOARCH=amd64 -ldflags="-s -w"`

### 联系方式
提供以下信息以获得更好的技术支持：
- 部署包版本号
- Linux发行版和版本
- 错误日志内容
- 网络环境信息

---

## 🎉 部署成功标志

当以下检查都通过时，表示部署成功：

- [ ] `./deploy.sh status` 显示服务运行中
- [ ] `curl http://localhost:8080/health` 返回健康状态
- [ ] 管理后台可以正常访问
- [ ] API测试工具可以正常使用
- [ ] 搜题接口可以正常调用

**🚀 恭喜！您的拍照搜题API服务已成功部署到Linux服务器！**
