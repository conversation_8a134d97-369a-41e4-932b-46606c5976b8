package utils

import (
	"crypto/rand"
	"crypto/subtle"
	"encoding/base64"
	"fmt"
	"regexp"
	"strings"
	"time"
)

// SecurityConfig 安全配置
type SecurityConfig struct {
	MaxLoginAttempts    int           // 最大登录尝试次数
	LoginLockoutTime    time.Duration // 登录锁定时间
	PasswordMinLength   int           // 密码最小长度
	PasswordMaxAge      time.Duration // 密码最大有效期
	SessionTimeout      time.Duration // 会话超时时间
	APIKeyRotationDays  int           // API密钥轮换天数
}

// DefaultSecurityConfig 默认安全配置
var DefaultSecurityConfig = SecurityConfig{
	MaxLoginAttempts:    5,
	LoginLockoutTime:    15 * time.Minute,
	PasswordMinLength:   8,
	PasswordMaxAge:      90 * 24 * time.Hour, // 90天
	SessionTimeout:      24 * time.Hour,      // 24小时
	APIKeyRotationDays:  30,                  // 30天
}

// GenerateSecureToken 生成安全令牌
func GenerateSecureToken(length int) (string, error) {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(bytes), nil
}

// ValidateAPIKey 验证API密钥格式
func ValidateAPIKey(apiKey string) error {
	if len(apiKey) < 32 {
		return fmt.Errorf("API密钥长度不能少于32位")
	}
	
	// 检查是否包含非法字符
	matched, _ := regexp.MatchString("^[a-zA-Z0-9_-]+$", apiKey)
	if !matched {
		return fmt.Errorf("API密钥包含非法字符")
	}
	
	return nil
}

// SecureCompare 安全比较字符串（防时序攻击）
func SecureCompare(a, b string) bool {
	return subtle.ConstantTimeCompare([]byte(a), []byte(b)) == 1
}

// SanitizeInput 清理输入内容
func SanitizeInput(input string) string {
	// 移除危险字符
	input = strings.ReplaceAll(input, "<", "&lt;")
	input = strings.ReplaceAll(input, ">", "&gt;")
	input = strings.ReplaceAll(input, "\"", "&quot;")
	input = strings.ReplaceAll(input, "'", "&#x27;")
	input = strings.ReplaceAll(input, "&", "&amp;")
	
	return strings.TrimSpace(input)
}

// ValidateImageURL 验证图片URL安全性
func ValidateImageURL(url string) error {
	if url == "" {
		return fmt.Errorf("图片URL不能为空")
	}
	
	// 检查URL格式
	if !strings.HasPrefix(url, "http://") && !strings.HasPrefix(url, "https://") {
		return fmt.Errorf("图片URL必须以http://或https://开头")
	}
	
	// 检查是否包含危险字符
	dangerousChars := []string{"<", ">", "\"", "'", "&", ";", "|", "`"}
	for _, char := range dangerousChars {
		if strings.Contains(url, char) {
			return fmt.Errorf("图片URL包含非法字符")
		}
	}
	
	// 检查URL长度
	if len(url) > 2048 {
		return fmt.Errorf("图片URL长度不能超过2048字符")
	}
	
	return nil
}

// IsWeakPassword 检查是否为弱密码
func IsWeakPassword(password string) bool {
	weakPasswords := []string{
		"123456", "password", "123456789", "12345678",
		"12345", "1234567", "admin", "123123",
		"qwerty", "abc123", "password123", "admin123",
		"root", "guest", "test", "user",
	}
	
	lowerPassword := strings.ToLower(password)
	for _, weak := range weakPasswords {
		if lowerPassword == weak {
			return true
		}
	}
	
	return false
}

// GenerateCSRFToken 生成CSRF令牌
func GenerateCSRFToken() (string, error) {
	return GenerateSecureToken(32)
}

// ValidateCSRFToken 验证CSRF令牌
func ValidateCSRFToken(token, expected string) bool {
	if token == "" || expected == "" {
		return false
	}
	return SecureCompare(token, expected)
}

// LogSecurityEvent 记录安全事件
func LogSecurityEvent(event, userID, ip, details string) {
	// 这里可以集成到日志系统或安全监控系统
	timestamp := time.Now().Format("2006-01-02 15:04:05")
	fmt.Printf("[SECURITY] %s - Event: %s, User: %s, IP: %s, Details: %s\n", 
		timestamp, event, userID, ip, details)
}

// CheckPasswordAge 检查密码是否过期
func CheckPasswordAge(lastChanged time.Time) bool {
	return time.Since(lastChanged) > DefaultSecurityConfig.PasswordMaxAge
}

// ShouldRotateAPIKey 检查API密钥是否需要轮换
func ShouldRotateAPIKey(createdAt time.Time) bool {
	daysSinceCreation := int(time.Since(createdAt).Hours() / 24)
	return daysSinceCreation >= DefaultSecurityConfig.APIKeyRotationDays
}

// ValidateIPAddress 验证IP地址格式
func ValidateIPAddress(ip string) bool {
	// 简单的IP地址验证
	parts := strings.Split(ip, ".")
	if len(parts) != 4 {
		return false
	}
	
	for _, part := range parts {
		if len(part) == 0 || len(part) > 3 {
			return false
		}
		
		for _, char := range part {
			if char < '0' || char > '9' {
				return false
			}
		}
	}
	
	return true
}

// MaskSensitiveData 脱敏敏感数据
func MaskSensitiveData(data string, dataType string) string {
	switch dataType {
	case "phone":
		return MaskPhone(data)
	case "email":
		return maskEmail(data)
	case "api_key":
		return maskAPIKey(data)
	default:
		return data
	}
}

// maskEmail 邮箱脱敏
func maskEmail(email string) string {
	if !strings.Contains(email, "@") {
		return email
	}
	
	parts := strings.Split(email, "@")
	if len(parts[0]) <= 2 {
		return email
	}
	
	masked := parts[0][:2] + "***" + "@" + parts[1]
	return masked
}

// maskAPIKey API密钥脱敏
func maskAPIKey(apiKey string) string {
	if len(apiKey) <= 8 {
		return "***"
	}
	
	return apiKey[:4] + "***" + apiKey[len(apiKey)-4:]
}
