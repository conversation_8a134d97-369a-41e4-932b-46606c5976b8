package middleware

import (
	"html"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// SecurityHeaders 安全头中间件
func SecurityHeaders() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 防止点击劫持
		c.Header("X-Frame-Options", "DENY")
		
		// 防止MIME类型嗅探
		c.Header("X-Content-Type-Options", "nosniff")
		
		// XSS保护
		c.Header("X-XSS-Protection", "1; mode=block")
		
		// 强制HTTPS（生产环境）
		if gin.Mode() == gin.ReleaseMode {
			c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
		}
		
		// 内容安全策略
		c.Header("Content-Security-Policy", "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'")
		
		// 引用策略
		c.<PERSON>er("Referrer-Policy", "strict-origin-when-cross-origin")
		
		// 权限策略
		c.Header("Permissions-Policy", "geolocation=(), microphone=(), camera=()")
		
		c.Next()
	}
}

// XSSProtection XSS防护中间件
func XSSProtection() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 对查询参数进行HTML转义
		for _, values := range c.Request.URL.Query() {
			for i, value := range values {
				values[i] = html.EscapeString(value)
			}
		}

		c.Next()
	}
}

// CSRFProtection CSRF防护中间件
func CSRFProtection() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 跳过GET请求和健康检查
		if c.Request.Method == "GET" || strings.HasPrefix(c.Request.URL.Path, "/health") {
			c.Next()
			return
		}
		
		// 检查Referer头
		referer := c.GetHeader("Referer")
		origin := c.GetHeader("Origin")
		host := c.GetHeader("Host")
		
		if referer == "" && origin == "" {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Missing Referer or Origin header",
			})
			c.Abort()
			return
		}

		// 验证来源
		if origin != "" && !strings.Contains(origin, host) {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Invalid Origin header",
			})
			c.Abort()
			return
		}
		
		c.Next()
	}
}



// InputSanitization 输入清理中间件
func InputSanitization() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查常见的恶意输入模式
		maliciousPatterns := []string{
			"<script",
			"javascript:",
			"onload=",
			"onerror=",
			"eval(",
			"document.cookie",
			"DROP TABLE",
			"DELETE FROM",
			"INSERT INTO",
			"UPDATE SET",
		}
		
		// 检查查询参数
		for _, values := range c.Request.URL.Query() {
			for _, value := range values {
				lowerValue := strings.ToLower(value)
				for _, pattern := range maliciousPatterns {
					if strings.Contains(lowerValue, pattern) {
						c.JSON(http.StatusBadRequest, gin.H{
							"error": "Malicious input detected",
						})
						c.Abort()
						return
					}
				}
			}
		}
		
		c.Next()
	}
}

// FileUploadSecurity 文件上传安全中间件
func FileUploadSecurity() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 限制文件大小（10MB）
		c.Request.Body = http.MaxBytesReader(c.Writer, c.Request.Body, 10<<20)
		
		// 检查Content-Type
		contentType := c.GetHeader("Content-Type")
		if strings.Contains(contentType, "multipart/form-data") {
			// 解析multipart form
			if err := c.Request.ParseMultipartForm(10 << 20); err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"error": "File too large or invalid format",
				})
				c.Abort()
				return
			}
			
			// 检查文件类型
			if c.Request.MultipartForm != nil && c.Request.MultipartForm.File != nil {
				for _, files := range c.Request.MultipartForm.File {
					for _, file := range files {
						// 检查文件扩展名
						filename := strings.ToLower(file.Filename)
						allowedExts := []string{".jpg", ".jpeg", ".png", ".gif", ".pdf", ".txt"}
						
						allowed := false
						for _, ext := range allowedExts {
							if strings.HasSuffix(filename, ext) {
								allowed = true
								break
							}
						}
						
						if !allowed {
							c.JSON(http.StatusBadRequest, gin.H{
								"error": "File type not allowed",
							})
							c.Abort()
							return
						}
					}
				}
			}
		}
		
		c.Next()
	}
}
