package service

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"solve_api/internal/model"
	"solve_api/internal/repository"
	"strings"
	"time"
)

type AIService struct {
	modelConfigRepo *repository.ModelConfigRepository
	aiLogService    *AILogService
}

// NewAIService 创建AI服务实例
func NewAIService(modelConfigRepo *repository.ModelConfigRepository, aiLogService *AILogService) *AIService {
	return &AIService{
		modelConfigRepo: modelConfigRepo,
		aiLogService:    aiLogService,
	}
}

// QwenVLResult Qwen-VL调用结果
type QwenVLResult struct {
	Structure  *model.PreprocessedQuestion
	RawContent string // 原始content字符串
	LogID      string // 日志ID
}

// DeepseekResult Deepseek调用结果（支持S1需求）
type DeepseekResult struct {
	Analysis     string            // 题目解析
	Answer       string            // 正确答案（支持多选题格式：A,B,C）
	Options      map[string]string // 选项信息（支持A/B/C/D/Y/N）
	QuestionType string            // 题目类型
	RawContent   string            // 原始content字符串
}

// DeepSeek请求结构体定义（使用标准OpenAI格式）
type DeepSeekMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type DeepSeekResponseFormat struct {
	Type string `json:"type"`
}

type DeepSeekRequest struct {
	Model            string                  `json:"model"`
	Messages         []DeepSeekMessage       `json:"messages"`
	Temperature      *float64                `json:"temperature,omitempty"`
	TopP             *float64                `json:"top_p,omitempty"`
	TopK             *int                    `json:"top_k,omitempty"`
	MaxTokens        *int                    `json:"max_tokens,omitempty"`
	DoSample         *bool                   `json:"do_sample,omitempty"`
	ResponseFormat   *DeepSeekResponseFormat `json:"response_format,omitempty"`
	FrequencyPenalty *float64                `json:"frequency_penalty,omitempty"`
	PresencePenalty  *float64                `json:"presence_penalty,omitempty"`
	Stream           *bool                   `json:"stream,omitempty"`
	Stop             interface{}             `json:"stop,omitempty"`
}

// CallQwenVL 调用Qwen-VL模型进行图像识别
func (s *AIService) CallQwenVL(imageURL string) (*QwenVLResult, error) {
	// 创建日志条目
	var logID string
	if s.aiLogService != nil {
		logID = s.aiLogService.CreateLogEntry()
	}

	// 获取模型配置
	modelConfig, err := s.modelConfigRepo.GetEnabledByName(model.ModelNameQwenVLPlus)
	if err != nil {
		return nil, fmt.Errorf("获取Qwen-VL模型配置失败: %w", err)
	}
	if modelConfig == nil {
		return nil, fmt.Errorf("Qwen-VL模型未配置或未启用")
	}

	// 构建请求参数
	params, err := modelConfig.GetParamsMap()
	if err != nil {
		return nil, fmt.Errorf("解析模型参数失败: %w", err)
	}

	// 从参数中获取动态配置的消息内容
	systemMessage := "精准且完整的识别题目内容，严格标准返回json格式。示例{\"question_type\": \"单选题or多选题or判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\"}}"
	userMessage := "question_text内的值不应该出现题目类型以及问题序号。"

	// 如果参数中有自定义消息，则使用自定义消息
	if sysMsg, exists := params["system_message"]; exists {
		if sysStr, ok := sysMsg.(string); ok && sysStr != "" {
			systemMessage = sysStr
		}
	}
	if usrMsg, exists := params["user_message"]; exists {
		if usrStr, ok := usrMsg.(string); ok && usrStr != "" {
			userMessage = usrStr
		}
	}

	// 获取模型名称
	modelName := "qwen-vl-plus" // 默认值
	if model, exists := params["model"]; exists {
		if modelStr, ok := model.(string); ok && modelStr != "" {
			modelName = modelStr
		}
	}

	// 添加Qwen API支持的技术约束参数（放在parameters对象中）
	// 严格按照Qwen API文档，只添加支持的技术参数
	parameters := make(map[string]interface{})
	supportedParams := []string{
		"temperature", "top_p", "top_k", "do_sample",
		"response_format", "frequency_penalty", "presence_penalty",
		"result_format",
	}

	for _, paramName := range supportedParams {
		if value, exists := params[paramName]; exists {
			parameters[paramName] = value
		}
	}

	// 确保result_format为message（DashScope格式要求）
	parameters["result_format"] = "message"

	// 定义消息结构体以确保字段顺序
	type MessageContent struct {
		Image string `json:"image,omitempty"`
		Text  string `json:"text,omitempty"`
	}

	type Message struct {
		Role    string      `json:"role"`
		Content interface{} `json:"content"`
	}

	type InputData struct {
		Messages []Message `json:"messages"`
	}

	type RequestBody struct {
		Model      string      `json:"model"`
		Parameters interface{} `json:"parameters"`
		Input      InputData   `json:"input"`
	}

	// 构建符合Qwen API DashScope格式的请求体
	// 按照要求的顺序：1.model 2.parameters 3.input.messages
	requestBody := RequestBody{
		Model:      modelName,
		Parameters: parameters,
		Input: InputData{
			Messages: []Message{
				{
					Role:    "system",
					Content: systemMessage, // 取数据库内配置的system_message
				},
				{
					Role: "user",
					Content: []MessageContent{
						{
							Image: imageURL, // 取用户在业务中提交的图片url
						},
						{
							Text: userMessage, // 取数据库内配置的user_message
						},
					},
				},
			},
		},
	}

	// 记录请求数据到日志
	if s.aiLogService != nil {
		s.aiLogService.LogQwenRequest(logID, requestBody)
	}

	// 发送请求
	apiURL := "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation"
	response, err := s.sendRequest(apiURL, modelConfig.ApiKey, requestBody)
	if err != nil {
		return nil, fmt.Errorf("调用Qwen-VL模型失败: %w", err)
	}

	// 解析响应
	var qwenResponse model.QwenVLResponse
	if err := json.Unmarshal(response, &qwenResponse); err != nil {
		return nil, fmt.Errorf("解析Qwen-VL响应失败: %w", err)
	}

	if len(qwenResponse.Output.Choices) == 0 {
		return nil, fmt.Errorf("Qwen-VL模型返回空结果")
	}

	// 提取内容字符串
	content := qwenResponse.GetContentString()

	// 记录响应数据到日志
	if s.aiLogService != nil {
		s.aiLogService.LogQwenResponse(logID, content)
	}

	if content == "" {
		return nil, fmt.Errorf("Qwen-VL模型返回空内容")
	}

	// 解析响应结构
	structure, err := s.parseQwenResponse(content)
	if err != nil {
		return nil, fmt.Errorf("解析Qwen响应失败: %w", err)
	}

	return &QwenVLResult{
		Structure:  &structure,
		RawContent: content,
		LogID:      logID,
	}, nil
}

// CallDeepseek 调用Deepseek模型进行题目解析
func (s *AIService) CallDeepseek(qwenResult *QwenVLResult) (*DeepseekResult, error) {
	return s.CallDeepseekWithLogID(qwenResult, "")
}

// CallDeepseekWithLogID 调用Deepseek模型进行题目解析（带日志ID）
func (s *AIService) CallDeepseekWithLogID(qwenResult *QwenVLResult, logID string) (*DeepseekResult, error) {
	// 获取模型配置
	modelConfig, err := s.modelConfigRepo.GetEnabledByName(model.ModelNameDeepseekChat)
	if err != nil {
		return nil, fmt.Errorf("获取Deepseek模型配置失败: %w", err)
	}
	if modelConfig == nil {
		return nil, fmt.Errorf("Deepseek模型未配置或未启用")
	}

	// 构建请求参数
	params, err := modelConfig.GetParamsMap()
	if err != nil {
		return nil, fmt.Errorf("解析模型参数失败: %w", err)
	}

	// 获取配置的消息内容
	systemMessage, _ := params["system_message"].(string)
	if systemMessage == "" {
		systemMessage = "你是一个专业的题目解析助手，请对提供的题目进行详细解析。"
	}

	userMessage, _ := params["user_message"].(string)
	if userMessage == "" {
		userMessage = "请以JSON格式返回，包含analysis（详细解题思路）和answer（最终答案）字段。"
	}

	// 构建完整的用户消息，只使用Qwen的原始响应内容，避免重复
	// qwenResult.RawContent 已经包含了完整的JSON数据，无需重复添加Structure
	fullUserMessage := fmt.Sprintf(`%s

以下是图像识别系统提取的题目信息：
%s

请基于以上信息进行详细的题目解析。`, userMessage, qwenResult.RawContent)

	// 构建标准OpenAI格式的请求体
	requestBody := DeepSeekRequest{
		Model: modelConfig.Name,
		Messages: []DeepSeekMessage{
			{
				Role:    "system",
				Content: systemMessage,
			},
			{
				Role:    "user",
				Content: fullUserMessage,
			},
		},
	}

	// 支持的DeepSeek API参数列表
	supportedParams := map[string]bool{
		"temperature":       true,
		"top_p":             true,
		"top_k":             true,
		"max_tokens":        true,
		"do_sample":         true,
		"response_format":   true,
		"frequency_penalty": true,
		"presence_penalty":  true,
		"stream":            true,
		"stop":              true,
	}

	// 只处理数据库中配置的且API支持的参数
	if temp, exists := params["temperature"]; exists && supportedParams["temperature"] {
		if tempFloat, ok := temp.(float64); ok {
			requestBody.Temperature = &tempFloat
		}
	}

	if topP, exists := params["top_p"]; exists && supportedParams["top_p"] {
		if topPFloat, ok := topP.(float64); ok {
			requestBody.TopP = &topPFloat
		}
	}

	if topK, exists := params["top_k"]; exists && supportedParams["top_k"] {
		if topKFloat, ok := topK.(float64); ok {
			topKInt := int(topKFloat)
			requestBody.TopK = &topKInt
		} else if topKInt, ok := topK.(int); ok {
			requestBody.TopK = &topKInt
		}
	}

	if maxTokens, exists := params["max_tokens"]; exists && supportedParams["max_tokens"] {
		if maxTokensFloat, ok := maxTokens.(float64); ok {
			maxTokensInt := int(maxTokensFloat)
			requestBody.MaxTokens = &maxTokensInt
		} else if maxTokensInt, ok := maxTokens.(int); ok {
			requestBody.MaxTokens = &maxTokensInt
		}
	}

	if doSample, exists := params["do_sample"]; exists && supportedParams["do_sample"] {
		if doSampleBool, ok := doSample.(bool); ok {
			requestBody.DoSample = &doSampleBool
		}
	}

	// 处理response_format参数
	if responseFormat, exists := params["response_format"]; exists && supportedParams["response_format"] {
		if responseFormatMap, ok := responseFormat.(map[string]interface{}); ok {
			if formatType, typeExists := responseFormatMap["type"].(string); typeExists {
				requestBody.ResponseFormat = &DeepSeekResponseFormat{Type: formatType}
			}
		} else if responseFormatMap, ok := responseFormat.(map[string]string); ok {
			if formatType, typeExists := responseFormatMap["type"]; typeExists {
				requestBody.ResponseFormat = &DeepSeekResponseFormat{Type: formatType}
			}
		}
	}

	// 处理惩罚参数
	if freqPenalty, exists := params["frequency_penalty"]; exists && supportedParams["frequency_penalty"] {
		if freqPenaltyFloat, ok := freqPenalty.(float64); ok {
			requestBody.FrequencyPenalty = &freqPenaltyFloat
		}
	}

	if presPenalty, exists := params["presence_penalty"]; exists && supportedParams["presence_penalty"] {
		if presPenaltyFloat, ok := presPenalty.(float64); ok {
			requestBody.PresencePenalty = &presPenaltyFloat
		}
	}

	// 处理stream参数
	if stream, exists := params["stream"]; exists && supportedParams["stream"] {
		if streamBool, ok := stream.(bool); ok {
			requestBody.Stream = &streamBool
		}
	}

	// 处理stop参数
	if stop, exists := params["stop"]; exists && supportedParams["stop"] {
		requestBody.Stop = stop
	}

	// 记录请求数据到日志
	if s.aiLogService != nil && logID != "" {
		s.aiLogService.LogDeepSeekRequest(logID, requestBody)
	}

	// 发送请求
	response, err := s.sendRequest(modelConfig.ApiURL, modelConfig.ApiKey, requestBody)
	if err != nil {
		return nil, fmt.Errorf("调用Deepseek模型失败: %w", err)
	}

	// 解析响应
	var deepseekResponse model.DeepseekResponse
	if err := json.Unmarshal(response, &deepseekResponse); err != nil {
		return nil, fmt.Errorf("解析Deepseek响应失败: %w", err)
	}

	if len(deepseekResponse.Choices) == 0 {
		return nil, fmt.Errorf("Deepseek模型返回空结果")
	}

	content := deepseekResponse.Choices[0].Message.Content

	// 记录响应数据到日志
	if s.aiLogService != nil && logID != "" {
		s.aiLogService.LogDeepSeekResponse(logID, content)
	}

	// 基础验证
	if strings.TrimSpace(content) == "" {
		return nil, fmt.Errorf("DeepSeek响应内容为空")
	}
	if len(strings.TrimSpace(content)) < 5 {
		return nil, fmt.Errorf("DeepSeek响应内容过短: %s", content)
	}

	// 尝试解析JSON格式的响应
	var rawResponse map[string]interface{}
	if err := json.Unmarshal([]byte(content), &rawResponse); err != nil {
		// 如果不是JSON格式，作为纯文本处理
		if len(strings.TrimSpace(content)) < 10 {
			return nil, fmt.Errorf("DeepSeek返回内容过短，可能是错误响应: %s", content)
		}

		// 作为纯文本处理
		return &DeepseekResult{
			Analysis:   content,
			Answer:     "请参考解析内容",
			RawContent: content,
		}, nil
	}

	// 解析DeepSeek响应
	result := s.parseDeepSeekResponse(rawResponse)

	// 最终验证：确保至少有一些有用的内容
	if strings.TrimSpace(result.Analysis) == "" && strings.TrimSpace(result.Answer) == "" {
		result.Analysis = content
		result.Answer = "请参考解析内容"
	}

	result.RawContent = content
	return result, nil
}

// sendRequest 发送HTTP请求
func (s *AIService) sendRequest(apiURL, apiKey string, requestBody interface{}) ([]byte, error) {
	// 序列化请求体
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求体失败: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+apiKey)

	// 发送请求
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	return body, nil
}

// validateDeepSeekResponse 简化的DeepSeek响应验证（仅保留必要的基础检查）
func (s *AIService) validateDeepSeekResponse(content string) error {
	// 只做最基础的检查，避免过度验证
	if strings.TrimSpace(content) == "" {
		return fmt.Errorf("响应内容为空")
	}
	if len(strings.TrimSpace(content)) < 5 {
		return fmt.Errorf("响应内容过短: %s", content)
	}
	return nil
}

// ValidateImageURL 验证图片URL是否可访问
func (s *AIService) ValidateImageURL(imageURL string) error {
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	resp, err := client.Head(imageURL)
	if err != nil {
		return fmt.Errorf("无法访问图片URL: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("图片URL返回错误状态码: %d", resp.StatusCode)
	}

	// 检查Content-Type是否为图片
	contentType := resp.Header.Get("Content-Type")
	if contentType == "" {
		return fmt.Errorf("无法确定文件类型")
	}

	validTypes := []string{
		"image/jpeg",
		"image/jpg",
		"image/png",
		"image/gif",
		"image/bmp",
		"image/webp",
	}

	for _, validType := range validTypes {
		if contentType == validType {
			return nil
		}
	}

	return fmt.Errorf("不支持的图片格式: %s", contentType)
}

// parseQwenResponse 解析Qwen响应，支持中文和英文字段名
func (s *AIService) parseQwenResponse(content string) (model.PreprocessedQuestion, error) {
	var structure model.PreprocessedQuestion

	// 解析原始JSON数据
	var rawData map[string]interface{}
	if err := json.Unmarshal([]byte(content), &rawData); err != nil {
		// 如果JSON解析完全失败，作为纯文本处理
		return model.PreprocessedQuestion{
			QuestionText: content,
			Subject:      "未知",
			Grade:        "未知",
			Difficulty:   3, // 默认中等难度
		}, nil
	}

	// 提取题目类型（支持中英文字段名）
	if questionType, exists := rawData["question_type"]; exists {
		if typeStr, ok := questionType.(string); ok {
			structure.QuestionType = typeStr
		}
	} else if questionType, exists := rawData["类型"]; exists {
		if typeStr, ok := questionType.(string); ok {
			structure.QuestionType = typeStr
		}
	}

	// 提取题目内容（支持中英文字段名）
	if questionText, exists := rawData["question_text"]; exists {
		if textStr, ok := questionText.(string); ok {
			structure.QuestionText = textStr
		}
	} else if questionText, exists := rawData["题目"]; exists {
		if textStr, ok := questionText.(string); ok {
			structure.QuestionText = textStr
		}
	}

	// 提取选项到分散字段
	if option, exists := rawData["A"]; exists {
		if optionStr, ok := option.(string); ok && strings.TrimSpace(optionStr) != "" {
			structure.OptionsA = optionStr
		}
	}
	if option, exists := rawData["B"]; exists {
		if optionStr, ok := option.(string); ok && strings.TrimSpace(optionStr) != "" {
			structure.OptionsB = optionStr
		}
	}
	if option, exists := rawData["C"]; exists {
		if optionStr, ok := option.(string); ok && strings.TrimSpace(optionStr) != "" {
			structure.OptionsC = optionStr
		}
	}
	if option, exists := rawData["D"]; exists {
		if optionStr, ok := option.(string); ok && strings.TrimSpace(optionStr) != "" {
			structure.OptionsD = optionStr
		}
	}
	if option, exists := rawData["Y"]; exists {
		if optionStr, ok := option.(string); ok && strings.TrimSpace(optionStr) != "" {
			structure.OptionsY = optionStr
		}
	}
	if option, exists := rawData["N"]; exists {
		if optionStr, ok := option.(string); ok && strings.TrimSpace(optionStr) != "" {
			structure.OptionsN = optionStr
		}
	}

	// 检查是否解析到有效数据
	if structure.QuestionText == "" && structure.QuestionType == "" {
		return model.PreprocessedQuestion{
			QuestionText: content,
			Subject:      "未知",
			Grade:        "未知",
			Difficulty:   3,
		}, nil
	}

	// 设置默认值
	if structure.Subject == "" {
		structure.Subject = "未知"
	}
	if structure.Grade == "" {
		structure.Grade = "未知"
	}
	if structure.Difficulty == 0 {
		structure.Difficulty = 3 // 默认中等难度
	}

	return structure, nil
}

// parseDeepSeekResponse 灵活的DeepSeek响应解析，支持多种字段格式
func (s *AIService) parseDeepSeekResponse(rawResponse map[string]interface{}) *DeepseekResult {
	result := &DeepseekResult{}

	// 提取分析字段（支持多种字段名）
	analysisFields := []string{"analysis", "解析", "分析", "explanation"}
	for _, field := range analysisFields {
		if analysis, exists := rawResponse[field]; exists {
			if str, ok := analysis.(string); ok && strings.TrimSpace(str) != "" {
				result.Analysis = str
				break
			}
		}
	}

	// 直接提取answer字段，支持多选题JSON格式
	result.Answer = s.extractAnswerFromResponse(rawResponse)

	// 直接提取选项信息
	result.Options = s.extractOptionsFromResponse(rawResponse)

	// 直接提取题目类型
	if questionType, exists := rawResponse["type"]; exists {
		if str, ok := questionType.(string); ok && strings.TrimSpace(str) != "" {
			result.QuestionType = str
		}
	}

	return result
}

// extractAnswerFromResponse 从响应中提取答案（支持DeepSeek实际返回的answer对象格式）
func (s *AIService) extractAnswerFromResponse(rawResponse map[string]interface{}) string {
	// 1. 尝试提取标准answer字段
	answerFields := []string{"answer", "答案", "正确答案", "result"}
	for _, field := range answerFields {
		if value, exists := rawResponse[field]; exists {
			// 处理对象格式的答案（DeepSeek实际返回格式）
			if answerObj, ok := value.(map[string]interface{}); ok {
				var answerParts []string
				// 按顺序检查A、B、C、D、Y、N选项
				for _, option := range []string{"A", "B", "C", "D", "Y", "N"} {
					if content, hasOption := answerObj[option]; hasOption {
						if contentStr, ok := content.(string); ok && strings.TrimSpace(contentStr) != "" {
							// 格式：选项标签:选项内容
							answerParts = append(answerParts, fmt.Sprintf("%s:%s", option, contentStr))
						}
					}
				}
				if len(answerParts) > 0 {
					result := strings.Join(answerParts, ",")
					return result
				}
			}

			// 处理字符串格式的答案（降级处理，兼容旧格式）
			if str, ok := value.(string); ok && strings.TrimSpace(str) != "" {
				// 处理旧的DeepSeek格式（如"A/Y"）
				if strings.Contains(str, "/") {
					// 对于判断题，"A/Y"表示正确，"B/N"表示错误
					if str == "A/Y" {
						return "Y:正确"
					} else if str == "B/N" {
						return "N:错误"
					}
					// 其他格式保持原样
					return str
				}

				// 纯字符串答案，尝试从选项中找到对应内容
				if options := s.extractOptionsFromResponse(rawResponse); len(options) > 0 {
					if content, exists := options[str]; exists {
						result := fmt.Sprintf("%s:%s", str, content)
						return result
					}
				}

				return str
			}
		}
	}

	return ""
}

// extractOptionsFromResponse 简化的选项提取，直接从DeepSeek响应提取
func (s *AIService) extractOptionsFromResponse(rawResponse map[string]interface{}) map[string]string {
	options := make(map[string]string)

	// 直接从根级别提取选项，处理DeepSeek实际返回格式
	optionKeys := []string{"A", "B", "C", "D", "Y", "N", "A/Y", "B/N"}
	for _, key := range optionKeys {
		if value, exists := rawResponse[key]; exists {
			if str, ok := value.(string); ok && strings.TrimSpace(str) != "" {
				// 处理DeepSeek的A/Y、B/N格式
				if key == "A/Y" {
					options["Y"] = str
				} else if key == "B/N" {
					options["N"] = str
				} else {
					// 标准选项直接存储
					options[key] = str
				}
			}
		}
	}

	return options
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
