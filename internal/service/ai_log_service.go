package service

import (
	"encoding/json"
	"fmt"
	"sync"
	"time"
)

// AILogEntry AI模型日志条目
type AILogEntry struct {
	ID        string       `json:"id"`
	Timestamp string       `json:"timestamp"`
	Qwen      *AIModelData `json:"qwen,omitempty"`
	DeepSeek  *AIModelData `json:"deepseek,omitempty"`
}

// AIModelData AI模型数据
type AIModelData struct {
	Request  string `json:"request"`  // 原始请求数据
	Response string `json:"response"` // 原始响应Content数据
}

// AILogService AI日志服务
type AILogService struct {
	logs  []AILogEntry
	mutex sync.RWMutex
}

// NewAILogService 创建AI日志服务实例
func NewAILogService() *AILogService {
	return &AILogService{
		logs: make([]AILogEntry, 0),
	}
}

// CreateLogEntry 创建新的日志条目
func (s *AILogService) CreateLogEntry() string {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	logID := fmt.Sprintf("log_%d", time.Now().UnixNano())
	entry := AILogEntry{
		ID:        logID,
		Timestamp: time.Now().Format("2006-01-02 15:04:05"),
	}

	s.logs = append(s.logs, entry)
	return logID
}

// LogQwenRequest 记录Qwen请求数据
func (s *AILogService) LogQwenRequest(logID string, requestData interface{}) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	for i := range s.logs {
		if s.logs[i].ID == logID {
			if s.logs[i].Qwen == nil {
				s.logs[i].Qwen = &AIModelData{}
			}

			// 将请求数据序列化为JSON字符串
			if requestBytes, err := json.MarshalIndent(requestData, "", "  "); err == nil {
				s.logs[i].Qwen.Request = string(requestBytes)
			} else {
				s.logs[i].Qwen.Request = fmt.Sprintf("序列化失败: %v", err)
			}
			break
		}
	}
}

// LogQwenResponse 记录Qwen响应数据
func (s *AILogService) LogQwenResponse(logID string, responseContent string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	for i := range s.logs {
		if s.logs[i].ID == logID {
			if s.logs[i].Qwen == nil {
				s.logs[i].Qwen = &AIModelData{}
			}
			s.logs[i].Qwen.Response = responseContent
			break
		}
	}
}

// LogDeepSeekRequest 记录DeepSeek请求数据
func (s *AILogService) LogDeepSeekRequest(logID string, requestData interface{}) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	for i := range s.logs {
		if s.logs[i].ID == logID {
			if s.logs[i].DeepSeek == nil {
				s.logs[i].DeepSeek = &AIModelData{}
			}

			// 将请求数据序列化为JSON字符串
			if requestBytes, err := json.MarshalIndent(requestData, "", "  "); err == nil {
				s.logs[i].DeepSeek.Request = string(requestBytes)
			} else {
				s.logs[i].DeepSeek.Request = fmt.Sprintf("序列化失败: %v", err)
			}
			break
		}
	}
}

// LogDeepSeekResponse 记录DeepSeek响应数据
func (s *AILogService) LogDeepSeekResponse(logID string, responseContent string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	for i := range s.logs {
		if s.logs[i].ID == logID {
			if s.logs[i].DeepSeek == nil {
				s.logs[i].DeepSeek = &AIModelData{}
			}
			s.logs[i].DeepSeek.Response = responseContent
			break
		}
	}
}

// ClearLogs 清空所有日志
func (s *AILogService) ClearLogs() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.logs = make([]AILogEntry, 0)
}
