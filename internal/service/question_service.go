package service

import (
	"fmt"
	"solve_api/internal/model"
	"solve_api/internal/repository"
	"strings"
	"time"
)

type QuestionService struct {
	questionRepo      *repository.QuestionRepository
	questionCacheRepo *repository.QuestionCacheRepository
	aiService         *AIService
	userRepo          *repository.UserRepository
	balanceLogRepo    *repository.BalanceLogRepository
}

// NewQuestionService 创建题目服务实例
func NewQuestionService(
	questionRepo *repository.QuestionRepository,
	questionCacheRepo *repository.QuestionCacheRepository,
	aiService *AIService,
	userRepo *repository.UserRepository,
	balanceLogRepo *repository.BalanceLogRepository,
) *QuestionService {
	return &QuestionService{
		questionRepo:      questionRepo,
		questionCacheRepo: questionCacheRepo,
		aiService:         aiService,
		userRepo:          userRepo,
		balanceLogRepo:    balanceLogRepo,
	}
}

// Search 拍照搜题主要业务逻辑
func (s *QuestionService) Search(userID uint, req *model.QuestionSearchRequest) (*model.QuestionSearchResponse, error) {
	startTime := time.Now()

	// 1. 验证图片URL
	if err := s.aiService.ValidateImageURL(req.ImageURL); err != nil {
		return nil, fmt.Errorf("图片URL验证失败: %w", err)
	}

	// 2. 调用Qwen-VL获取题目结构
	qwenResult, err := s.aiService.CallQwenVL(req.ImageURL)
	if err != nil {
		return nil, fmt.Errorf("图像识别失败: %w", err)
	}
	// 3. 预处理Qwen返回的数据
	preprocessed, err := model.PreprocessQwenResult(qwenResult.Structure)
	if err != nil || !preprocessed.IsValid {
		return nil, fmt.Errorf(preprocessed.ErrorMessage)
	}

	// 4. 基于预处理后的内容生成缓存键
	cacheKey := model.GenerateCacheKeyFromPreprocessed(preprocessed)

	// 5. 查找Redis缓存
	questions, err := s.questionCacheRepo.GetWithAssociates(cacheKey)
	if err == nil && len(questions) > 0 {
		// Redis命中，增加响应次数并返回
		s.questionRepo.IncrementResponse(questions[0].ID)
		processTime := time.Since(startTime).Milliseconds()

		// 如果有关联题目，返回所有相关题目
		if len(questions) > 1 {
			return s.buildAssociatedResponse(questions, true, processTime), nil
		}
		return questions[0].ToSearchResponse(true, processTime), nil
	}

	// 6. Redis未命中，降级查找MySQL
	questions, err = s.questionRepo.GetWithAssociates(cacheKey)
	if err == nil && len(questions) > 0 {
		// MySQL命中，回传Redis并返回
		s.questionRepo.IncrementResponse(questions[0].ID)
		processTime := time.Since(startTime).Milliseconds()

		if len(questions) > 1 {
			return s.buildAssociatedResponse(questions, true, processTime), nil
		}
		return questions[0].ToSearchResponse(true, processTime), nil
	}

	// 7. MySQL也未命中，调用DeepSeek
	deepseekResult, err := s.aiService.CallDeepseekWithLogID(qwenResult, qwenResult.LogID)
	if err != nil {
		return nil, fmt.Errorf("题目解析失败: %w", err)
	}

	// 8. 创建新题目记录
	question := &model.Question{
		SourceModel: "qwen-vl-plus,deepseek-chat",
	}

	// 使用新的FromPreprocessedWithDeepSeek方法
	question.FromPreprocessedWithDeepSeek(preprocessed, req.ImageURL, qwenResult.RawContent, deepseekResult)

	// 9. 保存到题库表
	if err := s.questionRepo.Create(question); err != nil {
		return nil, fmt.Errorf("保存题目失败: %w", err)
	}

	// 10. 保存到Redis缓存
	if err := s.questionCacheRepo.Set(question.CacheKey, question); err != nil {
		// 缓存失败不影响主流程
	}

	// 11. 扣费处理
	if err := s.processPayment(userID, model.ApplicationTypePhotoSearch); err != nil {
		// 扣费失败不影响返回结果
	}

	// 12. 构建响应
	processTime := time.Since(startTime).Milliseconds()
	response := question.ToSearchResponse(false, processTime)

	return response, nil
}

// processPayment 处理扣费
func (s *QuestionService) processPayment(userID uint, serviceType int) error {
	// 获取用户信息
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return fmt.Errorf("查询用户信息失败: %w", err)
	}
	if user == nil {
		return fmt.Errorf("用户不存在")
	}

	// 获取服务价格（这里简化处理，使用固定价格）
	price := 0.01 // 默认0.01元/次

	// 检查余额
	if user.Balance < price {
		return fmt.Errorf("余额不足")
	}

	// 扣费
	user.Balance -= price
	if err := s.userRepo.Update(user); err != nil {
		return fmt.Errorf("扣费失败: %w", err)
	}

	// 记录余额变动日志
	balanceLog := &model.BalanceLog{
		UserID:      userID,
		Amount:      -price,
		Balance:     user.Balance,
		Type:        model.BalanceLogTypeConsume,
		Description: "拍照搜题服务费用",
		RelatedID:   0, // 可以关联题目ID
	}

	if err := s.balanceLogRepo.Create(balanceLog); err != nil {
		// 日志记录失败不影响主流程
		return nil
	}

	return nil
}

// GetByID 根据ID获取题目
func (s *QuestionService) GetByID(id uint) (*model.QuestionResponse, error) {
	question, err := s.questionRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("查询题目失败: %w", err)
	}
	if question == nil {
		return nil, fmt.Errorf("题目不存在")
	}

	return question.ToResponse(), nil
}

// GetList 获取题目列表
func (s *QuestionService) GetList(page, pageSize int) ([]*model.QuestionResponse, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	questions, total, err := s.questionRepo.List(offset, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("查询题目列表失败: %w", err)
	}

	var result []*model.QuestionResponse
	for _, question := range questions {
		result = append(result, question.ToResponse())
	}

	return result, total, nil
}

// GetBySubject 根据学科获取题目列表
func (s *QuestionService) GetBySubject(subject string, page, pageSize int) ([]*model.QuestionResponse, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	questions, total, err := s.questionRepo.GetBySubject(subject, offset, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("查询题目列表失败: %w", err)
	}

	var result []*model.QuestionResponse
	for _, question := range questions {
		result = append(result, question.ToResponse())
	}

	return result, total, nil
}

// Search 搜索题目
func (s *QuestionService) SearchQuestions(keyword string, page, pageSize int) ([]*model.QuestionResponse, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	questions, total, err := s.questionRepo.Search(keyword, offset, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("搜索题目失败: %w", err)
	}

	var result []*model.QuestionResponse
	for _, question := range questions {
		result = append(result, question.ToResponse())
	}

	return result, total, nil
}

// isDuplicateCacheKeyError 检查是否为重复缓存键错误
func isDuplicateCacheKeyError(err error) bool {
	if err == nil {
		return false
	}
	errStr := err.Error()
	return strings.Contains(errStr, "Duplicate entry") &&
		strings.Contains(errStr, "idx_questions_cache_key")
}

// GetStats 获取题目统计信息
func (s *QuestionService) GetStats() (map[string]interface{}, error) {
	// 获取总数
	totalCount, err := s.questionRepo.GetTotalCount()
	if err != nil {
		return nil, fmt.Errorf("获取题目总数失败: %w", err)
	}

	// 获取各学科统计
	subjectCount, err := s.questionRepo.GetCountBySubject()
	if err != nil {
		return nil, fmt.Errorf("获取学科统计失败: %w", err)
	}

	// 获取各年级统计
	gradeCount, err := s.questionRepo.GetCountByGrade()
	if err != nil {
		return nil, fmt.Errorf("获取年级统计失败: %w", err)
	}

	// 获取各难度统计
	difficultyCount, err := s.questionRepo.GetCountByDifficulty()
	if err != nil {
		return nil, fmt.Errorf("获取难度统计失败: %w", err)
	}

	// 获取缓存统计
	cacheStats, err := s.questionCacheRepo.GetCacheStats()
	if err != nil {
		cacheStats = map[string]interface{}{
			"redis_available": false,
			"total_keys":      0,
		}
	}

	return map[string]interface{}{
		"total_questions":  totalCount,
		"subject_count":    subjectCount,
		"grade_count":      gradeCount,
		"difficulty_count": difficultyCount,
		"cache_stats":      cacheStats,
	}, nil
}

// ClearCache 清空题目缓存
func (s *QuestionService) ClearCache() error {
	return s.questionCacheRepo.Clear()
}

// buildAssociatedResponse 构建关联题目响应
func (s *QuestionService) buildAssociatedResponse(questions []*model.Question, cacheHit bool, processTime int64) *model.QuestionSearchResponse {
	if len(questions) == 0 {
		return nil
	}

	// 主题目作为主要响应
	mainResponse := questions[0].ToSearchResponse(cacheHit, processTime)

	return mainResponse
}

// CreateManagementQuestion 创建题库管理题目
func (s *QuestionService) CreateManagementQuestion(req *model.QuestionManagementRequest) (*model.Question, error) {
	return s.questionRepo.CreateManagementQuestion(req)
}

// UpdateManagementQuestion 更新题库管理题目
func (s *QuestionService) UpdateManagementQuestion(id uint, req *model.QuestionUpdateRequest) (*model.Question, error) {
	// 检查题目是否存在
	question, err := s.questionRepo.GetByID(id)
	if err != nil {
		return nil, err
	}
	if question == nil {
		return nil, fmt.Errorf("题目不存在")
	}

	// 构建更新数据
	updates := make(map[string]interface{})
	if req.QuestionType != nil {
		updates["question_type"] = *req.QuestionType
	}
	if req.QuestionText != nil {
		updates["question_text"] = *req.QuestionText // 使用优化后的字段
	}
	if req.QuestionImgRaw != nil {
		updates["question_img_raw"] = *req.QuestionImgRaw
	}
	if req.OptionsA != nil {
		updates["options_a"] = *req.OptionsA
	}
	if req.OptionsB != nil {
		updates["options_b"] = *req.OptionsB
	}
	if req.OptionsC != nil {
		updates["options_c"] = *req.OptionsC
	}
	if req.OptionsD != nil {
		updates["options_d"] = *req.OptionsD
	}
	if req.OptionsY != nil {
		updates["options_y"] = *req.OptionsY
	}
	if req.OptionsN != nil {
		updates["options_n"] = *req.OptionsN
	}
	if req.Answer != nil {
		updates["answer"] = *req.Answer
	}
	if req.Analysis != nil {
		updates["analysis"] = *req.Analysis
	}
	// 移除associates字段的更新（已删除）

	// 执行更新
	if err := s.questionRepo.UpdateQuestion(id, updates); err != nil {
		return nil, fmt.Errorf("更新题目失败: %w", err)
	}

	// 返回更新后的题目
	return s.questionRepo.GetByID(id)
}

// GetQuestionByID 根据ID获取题目（用于管理）
func (s *QuestionService) GetQuestionByID(id uint) (*model.Question, error) {
	return s.questionRepo.GetByID(id)
}

// GetManagementQuestionList 获取题库管理题目列表
func (s *QuestionService) GetManagementQuestionList(page, limit int, questionType, keyword string) ([]*model.Question, int64, error) {
	offset := (page - 1) * limit

	// 这里需要在repository中实现相应的查询方法
	// 暂时使用现有的方法，后续可以扩展
	if keyword != "" {
		questions, total, err := s.questionRepo.Search(keyword, offset, limit)
		return questions, total, err
	}

	if questionType != "" {
		// 需要在repository中添加按类型查询的方法
		questions, total, err := s.questionRepo.List(offset, limit)
		return questions, total, err
	}

	return s.questionRepo.List(offset, limit)
}

// DeleteQuestion 删除题目
func (s *QuestionService) DeleteQuestion(id uint) error {
	// 检查题目是否存在
	question, err := s.questionRepo.GetByID(id)
	if err != nil {
		return err
	}
	if question == nil {
		return fmt.Errorf("题目不存在")
	}

	// 删除题目（软删除）
	return s.questionRepo.Delete(id)
}
