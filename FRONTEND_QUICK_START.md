# 🚀 前端快速接入指南

## 📋 基本信息

**接口地址**: `POST /api/v1/api/search`  
**认证方式**: 请求头认证（推荐）

## 🔑 认证配置

```javascript
const API_CONFIG = {
  baseURL: 'https://your-domain.com',
  appKey: 'your_app_key',      // 从管理后台获取
  secretKey: 'your_secret_key'  // 从管理后台获取
};
```

## 📝 请求格式

### 请求头
```http
Content-Type: application/json
X-App-Key: your_app_key
X-Secret-Key: your_secret_key
```

### 请求体
```json
{
  "image_url": "https://example.com/question.jpg"
}
```

## 📤 响应格式

```json
{
  "code": 200,
  "message": "搜题成功",
  "data": {
    "id": 12345,
    "question_text": "题目内容",
    "question_type": "单选题",
    "options": {
      "A": "选项A",
      "B": "选项B",
      "C": "选项C", 
      "D": "选项D"
    },
    "answer": "A:选项A",
    "analysis": "解析内容",
    "cache_hit": true,
    "process_time": 150
  }
}
```

## 💻 代码示例

### React Hook
```typescript
import { useState } from 'react';

interface QuestionData {
  id: number;
  question_text: string;
  question_type: string;
  options: Record<string, string>;
  answer: string;
  analysis: string;
  cache_hit: boolean;
  process_time: number;
}

export function useQuestionSearch() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const searchQuestion = async (imageUrl: string): Promise<QuestionData | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/v1/api/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-App-Key': API_CONFIG.appKey,
          'X-Secret-Key': API_CONFIG.secretKey
        },
        body: JSON.stringify({ image_url: imageUrl })
      });

      const result = await response.json();

      if (result.code === 200) {
        return result.data;
      } else {
        setError(result.message);
        return null;
      }
    } catch (err) {
      setError('网络请求失败');
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { searchQuestion, loading, error };
}
```

### Vue 3 Composition API
```typescript
import { ref } from 'vue';

export function useQuestionSearch() {
  const loading = ref(false);
  const error = ref<string | null>(null);

  const searchQuestion = async (imageUrl: string) => {
    loading.value = true;
    error.value = null;

    try {
      const response = await fetch('/api/v1/api/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-App-Key': API_CONFIG.appKey,
          'X-Secret-Key': API_CONFIG.secretKey
        },
        body: JSON.stringify({ image_url: imageUrl })
      });

      const result = await response.json();

      if (result.code === 200) {
        return result.data;
      } else {
        error.value = result.message;
        return null;
      }
    } catch (err) {
      error.value = '网络请求失败';
      return null;
    } finally {
      loading.value = false;
    }
  };

  return { searchQuestion, loading, error };
}
```

### 原生JavaScript
```javascript
async function searchQuestion(imageUrl) {
  try {
    const response = await fetch('/api/v1/api/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-App-Key': API_CONFIG.appKey,
        'X-Secret-Key': API_CONFIG.secretKey
      },
      body: JSON.stringify({ image_url: imageUrl })
    });

    const result = await response.json();

    if (result.code === 200) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('搜题失败:', error);
    throw error;
  }
}

// 使用示例
searchQuestion('https://example.com/question.jpg')
  .then(data => {
    console.log('题目:', data.question_text);
    console.log('选项:', data.options);
    console.log('答案:', data.answer);
  })
  .catch(error => {
    console.error('错误:', error.message);
  });
```

## ⚠️ 错误处理

### 常见错误码
| 状态码 | 说明 | 处理方式 |
|--------|------|----------|
| 400 | 参数错误 | 检查图片URL格式 |
| 401 | 认证失败 | 检查API密钥 |
| 402 | 余额不足 | 提示用户充值 |
| 429 | 请求过频 | 延迟重试 |
| 500 | 服务器错误 | 稍后重试 |

### 错误处理示例
```javascript
function handleError(error, code) {
  switch (code) {
    case 400:
      return '图片URL格式错误';
    case 401:
      return 'API密钥无效';
    case 402:
      return '余额不足，请充值';
    case 429:
      return '请求过于频繁，请稍后重试';
    case 500:
      return '服务器繁忙，请稍后重试';
    default:
      return '未知错误';
  }
}
```

## 🎨 UI组件示例

### React组件
```tsx
import React, { useState } from 'react';
import { useQuestionSearch } from './useQuestionSearch';

export function QuestionSearchComponent() {
  const [imageUrl, setImageUrl] = useState('');
  const [result, setResult] = useState(null);
  const { searchQuestion, loading, error } = useQuestionSearch();

  const handleSearch = async () => {
    if (!imageUrl) return;
    
    const data = await searchQuestion(imageUrl);
    setResult(data);
  };

  return (
    <div className="question-search">
      <div className="input-group">
        <input
          type="url"
          value={imageUrl}
          onChange={(e) => setImageUrl(e.target.value)}
          placeholder="请输入图片URL"
          className="image-url-input"
        />
        <button 
          onClick={handleSearch} 
          disabled={loading || !imageUrl}
          className="search-button"
        >
          {loading ? '搜索中...' : '搜题'}
        </button>
      </div>

      {error && (
        <div className="error-message">
          错误: {error}
        </div>
      )}

      {result && (
        <div className="result-container">
          <h3>题目</h3>
          <p>{result.question_text}</p>
          
          <h4>选项</h4>
          <ul>
            {Object.entries(result.options).map(([key, value]) => (
              <li key={key}>{key}: {value}</li>
            ))}
          </ul>
          
          <h4>答案</h4>
          <p>{result.answer}</p>
          
          <h4>解析</h4>
          <p>{result.analysis}</p>
          
          <div className="meta-info">
            <span>缓存命中: {result.cache_hit ? '是' : '否'}</span>
            <span>处理时间: {result.process_time}ms</span>
          </div>
        </div>
      )}
    </div>
  );
}
```

## 🔧 工具函数

### 图片URL验证
```javascript
function validateImageUrl(url) {
  const urlPattern = /^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i;
  return urlPattern.test(url);
}
```

### 防抖处理
```javascript
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

const debouncedSearch = debounce(searchQuestion, 1000);
```

### 重试机制
```javascript
async function searchWithRetry(imageUrl, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await searchQuestion(imageUrl);
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
}
```

## 📱 移动端适配

### 图片上传处理
```javascript
function handleImageUpload(file) {
  return new Promise((resolve, reject) => {
    const formData = new FormData();
    formData.append('image', file);
    
    // 上传到图片服务器
    fetch('/upload', {
      method: 'POST',
      body: formData
    })
    .then(response => response.json())
    .then(data => resolve(data.url))
    .catch(reject);
  });
}

// 使用示例
async function handleFileSelect(event) {
  const file = event.target.files[0];
  if (file) {
    try {
      const imageUrl = await handleImageUpload(file);
      const result = await searchQuestion(imageUrl);
      // 处理结果
    } catch (error) {
      console.error('处理失败:', error);
    }
  }
}
```

## 🚀 性能优化

### 请求缓存
```javascript
const cache = new Map();

async function searchQuestionWithCache(imageUrl) {
  if (cache.has(imageUrl)) {
    return cache.get(imageUrl);
  }
  
  const result = await searchQuestion(imageUrl);
  cache.set(imageUrl, result);
  
  // 设置缓存过期时间
  setTimeout(() => cache.delete(imageUrl), 5 * 60 * 1000); // 5分钟
  
  return result;
}
```

### 并发控制
```javascript
class RequestQueue {
  constructor(maxConcurrent = 3) {
    this.maxConcurrent = maxConcurrent;
    this.running = 0;
    this.queue = [];
  }
  
  async add(requestFn) {
    return new Promise((resolve, reject) => {
      this.queue.push({ requestFn, resolve, reject });
      this.process();
    });
  }
  
  async process() {
    if (this.running >= this.maxConcurrent || this.queue.length === 0) {
      return;
    }
    
    this.running++;
    const { requestFn, resolve, reject } = this.queue.shift();
    
    try {
      const result = await requestFn();
      resolve(result);
    } catch (error) {
      reject(error);
    } finally {
      this.running--;
      this.process();
    }
  }
}

const requestQueue = new RequestQueue(3);

// 使用队列
function queuedSearch(imageUrl) {
  return requestQueue.add(() => searchQuestion(imageUrl));
}
```

## 📞 获取API密钥

1. 注册账号并登录系统
2. 创建应用获取API密钥
3. 在代码中配置密钥信息
4. 开始调用接口

**管理后台地址**: `https://your-domain.com/web/admin.html`

---

**💡 提示**: 
- 请妥善保管API密钥，不要在前端代码中暴露
- 建议通过后端代理的方式调用API
- 遇到问题请查看完整的API文档或联系技术支持
