# 拍照搜题 API 服务

基于 Golang 开发的拍照搜题 Web API 服务，采用模块化设计，包含用户管理、应用管理、鉴权流控、模型服务、计费系统等核心功能模块。

## 项目概述

本项目是一个生产级的拍照搜题API服务解决方案，提供从用户注册到题目搜索的全流程服务，支持多种AI模型集成、灵活的计费策略。

### 🎯 核心功能

- **拍照搜题**: 支持图片上传，自动识别题目内容并提供详细解析
- **用户管理**: 完整的用户注册、登录、密码管理功能
- **应用管理**: 支持多应用管理，每个应用独立的API密钥
- **计费系统**: 灵活的价格配置和余额管理
- **限流控制**: 多层级限流保护，防止恶意调用
- **缓存优化**: Redis缓存提升响应速度

### 技术栈

- **语言**: Golang 1.21+
- **Web框架**: Gin
- **数据库**: MySQL 8.0
- **缓存**: Redis 7.0+
- **ORM**: GORM
- **日志**: zap + lumberjack
- **配置管理**: viper

## 项目结构

```
├── cmd/                  # 应用程序入口
│   └── main.go
├── config/               # 配置文件
│   ├── config.yaml       # 开发配置
│   └── config.prod.yaml  # 生产配置
├── internal/             # 私有应用和库代码
│   ├── api/              # API 层
│   ├── middleware/       # 中间件
│   ├── model/            # 数据模型
│   ├── repository/       # 数据访问层
│   ├── service/          # 业务逻辑层
│   ├── utils/            # 工具函数
│   ├── config/           # 配置管理
│   ├── cache/            # 缓存管理
│   └── database/         # 数据库连接
├── scripts/              # 部署脚本
├── web/                  # 管理界面
├── logs/                 # 日志文件目录
└── README.md            # 项目说明
```

## 部署指南

### 环境要求

- Go 1.21+
- MySQL 8.0+
- Redis 7.0+

### 编译部署

```bash
# 安装依赖
go mod tidy

# 编译
go build -o solve_api cmd/main.go

# 运行
./solve_api
```

### 配置说明

1. **数据库配置**：项目已配置远程MySQL和Redis服务器
2. **短信服务**：已集成阿里云短信服务
3. **AI模型**：已配置DeepSeek API密钥

### 配置文件

- 生产环境：`config/config.prod.yaml`
- 开发环境：`config/config.yaml`

### 启动服务

```bash
# 使用生产配置启动
CONFIG_FILE=config/config.prod.yaml ./solve_api
```

服务将在配置的端口启动（默认8080）。

## 核心功能

### 主要功能模块

- ✅ 用户注册与登录（手机号 + 验证码 + 密码）
- ✅ 应用管理（API密钥管理）
- ✅ 拍照搜题（图像识别 + AI解析）
- ✅ 计费系统（余额管理 + 扣费）
- ✅ 限流控制（多层级限流保护）
- ✅ 缓存优化（Redis缓存）
- ✅ 管理后台（模型配置 + 系统管理）

### 主要API接口

| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 拍照搜题 | POST | `/api/v1/api/search` | 核心搜题接口 |
| 用户注册 | POST | `/api/v1/user/register` | 用户注册 |
| 用户登录 | POST | `/api/v1/user/login` | 用户登录 |
| 健康检查 | GET | `/health` | 服务健康检查 |

## 管理后台

访问管理后台：`http://localhost:8080/web/admin.html`

默认管理员账号：
- 用户名：`15688515913`
- 密码：`admin888`

**注意：请立即修改默认密码**

## 配置说明

### 主要配置项

- `server.port`: 服务端口（默认8080）
- `server.mode`: 运行模式（release/debug）
- `database.mysql`: MySQL数据库配置
- `redis`: Redis缓存配置
- `app.invite_code`: 注册邀请码（默认SOLVE2024）
- `app.rate_limit`: API限流配置
- `log.level`: 日志级别（warn/info/debug）

### 环境变量

支持通过环境变量覆盖配置：

```bash
export CONFIG_FILE=config/config.prod.yaml
```

## 🔒 安全部署

**⚠️ 生产环境部署前必须完成安全配置！**

### 1. 环境变量配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑并设置所有必需的环境变量
vim .env
```

### 2. 安全检查
```bash
# 运行安全检查脚本
./security_check.sh
```

### 3. 修改默认密码
- 默认管理员：`15688515913` / `admin888`
- **必须立即修改为强密码！**

### 4. 文件权限设置
```bash
chmod 600 config/*.yaml .env
chmod +x solve_api deploy.sh security_check.sh
```

详细安全指南请参考：[SECURITY_GUIDE.md](./SECURITY_GUIDE.md)

## 注意事项

1. **安全第一**: 严格按照安全指南配置
2. **环境变量**: 所有敏感信息使用环境变量
3. **HTTPS**: 生产环境必须使用HTTPS
4. **定期更新**: 定期更新系统和依赖包
5. **监控日志**: 定期检查安全日志

## 许可证

本项目采用MIT许可证。
