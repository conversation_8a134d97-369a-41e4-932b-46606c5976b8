#!/bin/bash

# 生产环境安全检查脚本
# 使用方法: ./security_check.sh

echo "🔒 开始生产环境安全检查..."
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查结果统计
PASS_COUNT=0
WARN_COUNT=0
FAIL_COUNT=0

# 检查函数
check_pass() {
    echo -e "${GREEN}✓ PASS${NC}: $1"
    ((PASS_COUNT++))
}

check_warn() {
    echo -e "${YELLOW}⚠ WARN${NC}: $1"
    ((WARN_COUNT++))
}

check_fail() {
    echo -e "${RED}✗ FAIL${NC}: $1"
    ((FAIL_COUNT++))
}

echo "1. 检查配置文件安全..."
echo "--------------------------------"

# 检查配置文件权限
if [ -f "config/config.prod.yaml" ]; then
    PERM=$(stat -c "%a" config/config.prod.yaml)
    if [ "$PERM" = "600" ] || [ "$PERM" = "400" ]; then
        check_pass "生产配置文件权限正确 ($PERM)"
    else
        check_fail "生产配置文件权限不安全 ($PERM)，应设置为600"
    fi
else
    check_fail "生产配置文件不存在"
fi

# 检查环境变量文件
if [ -f ".env" ]; then
    PERM=$(stat -c "%a" .env)
    if [ "$PERM" = "600" ] || [ "$PERM" = "400" ]; then
        check_pass "环境变量文件权限正确 ($PERM)"
    else
        check_fail "环境变量文件权限不安全 ($PERM)，应设置为600"
    fi
    
    # 检查环境变量是否设置
    source .env 2>/dev/null
    required_vars=("MYSQL_PASSWORD" "REDIS_PASSWORD" "DEEPSEEK_API_KEY")
    for var in "${required_vars[@]}"; do
        if [ -n "${!var}" ]; then
            check_pass "环境变量 $var 已设置"
        else
            check_fail "环境变量 $var 未设置"
        fi
    done
else
    check_fail "环境变量文件 .env 不存在"
fi

echo ""
echo "2. 检查敏感信息泄露..."
echo "--------------------------------"

# 检查配置文件中是否还有明文密码
if grep -q "password.*:" config/config.prod.yaml | grep -v "\${"; then
    check_fail "配置文件中仍包含明文密码"
else
    check_pass "配置文件中未发现明文密码"
fi

# 检查API密钥
if grep -q "api_key.*sk-" config/config.prod.yaml; then
    check_fail "配置文件中包含明文API密钥"
else
    check_pass "配置文件中未发现明文API密钥"
fi

echo ""
echo "3. 检查文件权限..."
echo "--------------------------------"

# 检查可执行文件权限
if [ -f "solve_api" ]; then
    PERM=$(stat -c "%a" solve_api)
    if [ "$PERM" = "755" ] || [ "$PERM" = "750" ]; then
        check_pass "可执行文件权限正确 ($PERM)"
    else
        check_warn "可执行文件权限 ($PERM)，建议设置为755"
    fi
else
    check_warn "可执行文件 solve_api 不存在，需要先编译"
fi

# 检查部署脚本权限
if [ -f "deploy.sh" ]; then
    PERM=$(stat -c "%a" deploy.sh)
    if [ "$PERM" = "755" ] || [ "$PERM" = "750" ]; then
        check_pass "部署脚本权限正确 ($PERM)"
    else
        check_warn "部署脚本权限 ($PERM)，建议设置为755"
    fi
fi

echo ""
echo "4. 检查网络安全..."
echo "--------------------------------"

# 检查防火墙状态
if command -v ufw >/dev/null 2>&1; then
    if ufw status | grep -q "Status: active"; then
        check_pass "UFW防火墙已启用"
    else
        check_warn "UFW防火墙未启用"
    fi
else
    check_warn "未安装UFW防火墙"
fi

# 检查开放端口
OPEN_PORTS=$(netstat -tlnp 2>/dev/null | grep LISTEN | wc -l)
if [ "$OPEN_PORTS" -lt 10 ]; then
    check_pass "开放端口数量合理 ($OPEN_PORTS)"
else
    check_warn "开放端口较多 ($OPEN_PORTS)，请检查是否必要"
fi

echo ""
echo "5. 检查系统安全..."
echo "--------------------------------"

# 检查系统更新
if command -v apt >/dev/null 2>&1; then
    UPDATES=$(apt list --upgradable 2>/dev/null | wc -l)
    if [ "$UPDATES" -lt 5 ]; then
        check_pass "系统更新较少 ($UPDATES)"
    else
        check_warn "有较多系统更新可用 ($UPDATES)，建议及时更新"
    fi
fi

# 检查磁盘空间
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -lt 80 ]; then
    check_pass "磁盘使用率正常 ($DISK_USAGE%)"
elif [ "$DISK_USAGE" -lt 90 ]; then
    check_warn "磁盘使用率较高 ($DISK_USAGE%)"
else
    check_fail "磁盘使用率过高 ($DISK_USAGE%)，需要清理"
fi

# 检查内存使用
MEM_USAGE=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
if [ "$MEM_USAGE" -lt 80 ]; then
    check_pass "内存使用率正常 ($MEM_USAGE%)"
elif [ "$MEM_USAGE" -lt 90 ]; then
    check_warn "内存使用率较高 ($MEM_USAGE%)"
else
    check_fail "内存使用率过高 ($MEM_USAGE%)，需要优化"
fi

echo ""
echo "6. 检查应用安全..."
echo "--------------------------------"

# 检查日志目录
if [ -d "logs" ]; then
    LOG_PERM=$(stat -c "%a" logs)
    if [ "$LOG_PERM" = "755" ] || [ "$LOG_PERM" = "750" ]; then
        check_pass "日志目录权限正确 ($LOG_PERM)"
    else
        check_warn "日志目录权限 ($LOG_PERM)，建议设置为755"
    fi
else
    check_warn "日志目录不存在"
fi

# 检查进程状态
if pgrep -f "solve_api" > /dev/null; then
    check_pass "应用进程正在运行"
else
    check_warn "应用进程未运行"
fi

echo ""
echo "=================================="
echo "🔒 安全检查完成"
echo "=================================="
echo -e "通过: ${GREEN}$PASS_COUNT${NC}"
echo -e "警告: ${YELLOW}$WARN_COUNT${NC}"
echo -e "失败: ${RED}$FAIL_COUNT${NC}"
echo ""

if [ "$FAIL_COUNT" -gt 0 ]; then
    echo -e "${RED}❌ 发现严重安全问题，请立即修复！${NC}"
    exit 1
elif [ "$WARN_COUNT" -gt 0 ]; then
    echo -e "${YELLOW}⚠️  发现安全警告，建议优化${NC}"
    exit 2
else
    echo -e "${GREEN}✅ 安全检查全部通过${NC}"
    exit 0
fi
