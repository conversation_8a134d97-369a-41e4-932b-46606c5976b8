<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拍照搜题API测试工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }

        .panel {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            border: 1px solid #e9ecef;
        }

        .panel h3 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.3em;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }

        input, textarea, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }

        .btn {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }

        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .response-container {
            margin-top: 20px;
        }

        .response-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
        }

        .response-content {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
            line-height: 1.5;
            overflow-x: auto;
            white-space: pre-wrap;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .example-urls {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin-top: 10px;
        }

        .example-urls h4 {
            color: #856404;
            margin-bottom: 10px;
        }

        .example-url {
            display: block;
            color: #007bff;
            text-decoration: none;
            margin: 5px 0;
            cursor: pointer;
        }

        .example-url:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .content {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 拍照搜题API测试工具</h1>
            <p>快速测试和调试拍照搜题接口</p>
        </div>

        <div class="content">
            <div class="panel">
                <h3>📝 请求配置</h3>
                
                <div class="form-group">
                    <label for="authMethod">认证方式</label>
                    <select id="authMethod" onchange="toggleAuthMethod()">
                        <option value="header">请求头认证（推荐）</option>
                        <option value="body">请求体认证</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="appKey">App Key</label>
                    <input type="text" id="appKey" placeholder="输入您的App Key">
                </div>

                <div class="form-group">
                    <label for="secretKey">Secret Key</label>
                    <input type="password" id="secretKey" placeholder="输入您的Secret Key">
                </div>

                <div class="form-group">
                    <label for="imageUrl">图片URL</label>
                    <input type="url" id="imageUrl" placeholder="https://example.com/question.jpg">
                    
                    <div class="example-urls">
                        <h4>示例图片URL:</h4>
                        <a class="example-url" onclick="setImageUrl('https://via.placeholder.com/400x300/007bff/ffffff?text=Sample+Question')">
                            示例题目图片1
                        </a>
                        <a class="example-url" onclick="setImageUrl('https://via.placeholder.com/500x400/28a745/ffffff?text=Math+Problem')">
                            示例题目图片2
                        </a>
                    </div>
                </div>

                <button class="btn" onclick="testAPI()" id="testBtn">
                    🚀 发送请求
                </button>

                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>正在处理请求...</p>
                </div>
            </div>

            <div class="panel">
                <h3>📊 响应结果</h3>
                
                <div class="response-container" id="responseContainer" style="display: none;">
                    <div class="response-header">
                        <span>响应状态:</span>
                        <span class="status-badge" id="statusBadge">-</span>
                    </div>
                    <div class="response-content" id="responseContent">
                        等待请求...
                    </div>
                </div>

                <div id="placeholder" style="text-align: center; padding: 50px; color: #6c757d;">
                    <p>📡 配置参数并发送请求查看结果</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 从localStorage加载保存的配置
        window.onload = function() {
            const savedAppKey = localStorage.getItem('api_test_app_key');
            const savedSecretKey = localStorage.getItem('api_test_secret_key');
            
            if (savedAppKey) document.getElementById('appKey').value = savedAppKey;
            if (savedSecretKey) document.getElementById('secretKey').value = savedSecretKey;
        };

        function toggleAuthMethod() {
            // 认证方式切换逻辑（如果需要的话）
        }

        function setImageUrl(url) {
            document.getElementById('imageUrl').value = url;
        }

        async function testAPI() {
            const appKey = document.getElementById('appKey').value.trim();
            const secretKey = document.getElementById('secretKey').value.trim();
            const imageUrl = document.getElementById('imageUrl').value.trim();
            const authMethod = document.getElementById('authMethod').value;

            // 验证输入
            if (!appKey || !secretKey || !imageUrl) {
                alert('请填写所有必需字段');
                return;
            }

            // 保存配置到localStorage
            localStorage.setItem('api_test_app_key', appKey);
            localStorage.setItem('api_test_secret_key', secretKey);

            // 显示加载状态
            showLoading(true);

            try {
                let requestOptions = {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                };

                let requestBody = {
                    image_url: imageUrl
                };

                if (authMethod === 'header') {
                    // 请求头认证
                    requestOptions.headers['X-App-Key'] = appKey;
                    requestOptions.headers['X-Secret-Key'] = secretKey;
                } else {
                    // 请求体认证
                    requestBody.app_key = appKey;
                    requestBody.secret_key = secretKey;
                }

                requestOptions.body = JSON.stringify(requestBody);

                // 发送请求
                const response = await fetch('/api/v1/api/search', requestOptions);
                const result = await response.json();

                // 显示结果
                showResponse(response.status, result);

            } catch (error) {
                showResponse(0, {
                    error: '网络请求失败',
                    message: error.message
                });
            } finally {
                showLoading(false);
            }
        }

        function showLoading(show) {
            const loading = document.getElementById('loading');
            const testBtn = document.getElementById('testBtn');
            
            if (show) {
                loading.style.display = 'block';
                testBtn.disabled = true;
                testBtn.textContent = '请求中...';
            } else {
                loading.style.display = 'none';
                testBtn.disabled = false;
                testBtn.textContent = '🚀 发送请求';
            }
        }

        function showResponse(status, data) {
            const responseContainer = document.getElementById('responseContainer');
            const placeholder = document.getElementById('placeholder');
            const statusBadge = document.getElementById('statusBadge');
            const responseContent = document.getElementById('responseContent');

            // 隐藏占位符，显示响应容器
            placeholder.style.display = 'none';
            responseContainer.style.display = 'block';

            // 设置状态标识
            if (status >= 200 && status < 300) {
                statusBadge.textContent = `${status} Success`;
                statusBadge.className = 'status-badge status-success';
            } else {
                statusBadge.textContent = `${status} Error`;
                statusBadge.className = 'status-badge status-error';
            }

            // 格式化并显示响应内容
            responseContent.textContent = JSON.stringify(data, null, 2);
        }

        // 快捷键支持
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'Enter') {
                testAPI();
            }
        });
    </script>
</body>
</html>
