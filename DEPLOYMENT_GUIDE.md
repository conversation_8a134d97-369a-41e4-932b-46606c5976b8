# 🚀 生产环境部署包使用指南

## 📦 部署包内容

已为您打包好完整的生产环境部署包，包含：

### 📁 文件结构
```
solve_api_YYYYMMDD_HHMMSS/
├── solve_api                 # 可执行文件（已编译）
├── .env                      # 环境变量配置文件
├── config/
│   └── config.prod.yaml     # 生产环境配置
├── deploy.sh                # 部署管理脚本
├── security_check.sh        # 安全检查脚本
├── web/
│   ├── admin.html           # 管理后台
│   └── api_test.html        # API测试工具
├── scripts/                 # 数据库脚本
├── logs/                    # 日志目录
├── INSTALL.md               # 详细安装说明
├── README.md                # 项目说明
├── SECURITY_GUIDE.md        # 安全指南
├── API_DOCS.md              # API文档
└── VERSION                  # 版本信息
```

## 🎯 快速部署（5分钟上线）

### 1. 上传部署包
```bash
# 上传到服务器
scp solve_api_YYYYMMDD_HHMMSS.tar.gz user@your-server:/opt/

# 登录服务器
ssh user@your-server

# 解压部署包
cd /opt
tar -xzf solve_api_YYYYMMDD_HHMMSS.tar.gz
cd solve_api_YYYYMMDD_HHMMSS
```

### 2. 配置环境变量
```bash
# 编辑环境变量文件
vim .env

# 必须修改的配置：
# MYSQL_PASSWORD=your_mysql_password
# REDIS_PASSWORD=your_redis_password  
# QWEN_API_KEY=your_qwen_api_key
# DEEPSEEK_API_KEY=your_deepseek_api_key
# JWT_SECRET=your_jwt_secret
# ADMIN_DEFAULT_PASSWORD=your_admin_password
```

### 3. 设置权限
```bash
chmod +x solve_api deploy.sh security_check.sh
chmod 600 .env config/config.prod.yaml
```

### 4. 安全检查
```bash
./security_check.sh
```

### 5. 启动服务
```bash
./deploy.sh start
```

### 6. 验证部署
```bash
# 检查服务状态
./deploy.sh status

# 测试健康检查
curl http://localhost:8080/health

# 访问管理后台
curl http://localhost:8080/web/admin.html
```

## 🔧 服务管理

### 基本命令
```bash
./deploy.sh start      # 启动服务
./deploy.sh stop       # 停止服务  
./deploy.sh restart    # 重启服务
./deploy.sh status     # 查看状态
./deploy.sh logs       # 查看日志
```

### 日志查看
```bash
# 实时日志
./deploy.sh logs

# 历史日志
cat logs/app.log

# 错误日志
grep -i error logs/app.log
```

## 🔒 安全配置

### 1. 立即修改默认密码
- **管理员账号**: `15688515913`
- **默认密码**: `admin888`
- **⚠️ 必须立即修改！**

### 2. 配置防火墙
```bash
# Ubuntu/Debian
ufw allow 22/tcp      # SSH
ufw allow 80/tcp      # HTTP
ufw allow 443/tcp     # HTTPS
ufw allow 8080/tcp    # 应用端口
ufw enable

# CentOS/RHEL
firewall-cmd --permanent --add-port=22/tcp
firewall-cmd --permanent --add-port=80/tcp
firewall-cmd --permanent --add-port=443/tcp
firewall-cmd --permanent --add-port=8080/tcp
firewall-cmd --reload
```

### 3. 配置HTTPS（推荐）
```bash
# 使用Let's Encrypt
sudo apt install certbot nginx
sudo certbot --nginx -d your-domain.com

# 配置nginx反向代理
sudo vim /etc/nginx/sites-available/solve_api
```

## 🌐 访问地址

部署成功后，可以访问以下地址：

- **健康检查**: `http://your-server:8080/health`
- **管理后台**: `http://your-server:8080/web/admin.html`
- **API测试**: `http://your-server:8080/api-test`
- **搜题接口**: `http://your-server:8080/api/v1/api/search`

## 📊 监控和维护

### 系统监控
```bash
# 检查进程
ps aux | grep solve_api

# 检查端口
netstat -tlnp | grep 8080

# 检查资源使用
top -p $(pgrep solve_api)

# 检查磁盘空间
df -h

# 检查内存使用
free -h
```

### 定期维护
```bash
# 每周执行安全检查
./security_check.sh

# 每月清理日志
find logs/ -name "*.log" -mtime +30 -delete

# 备份配置
tar -czf backup_$(date +%Y%m%d).tar.gz .env config/
```

## 🆘 故障排除

### 常见问题

#### 1. 服务无法启动
```bash
# 检查端口占用
lsof -i :8080

# 检查配置文件
./security_check.sh

# 查看详细错误
./solve_api 2>&1 | head -20
```

#### 2. 数据库连接失败
```bash
# 测试数据库连接
mysql -h *********** -P 3380 -u gmdns -p

# 检查网络连通性
telnet *********** 3380
```

#### 3. Redis连接失败
```bash
# 测试Redis连接
redis-cli -h r-bp1t323p6w8yn2cpq0pd.redis.rds.aliyuncs.com -p 6379 -a SsYZyxSSr8uEVWKJ ping
```

#### 4. API调用失败
```bash
# 检查API密钥配置
grep -i api .env

# 测试API接口
curl -X POST http://localhost:8080/api/v1/api/search \
  -H "Content-Type: application/json" \
  -H "X-App-Key: your_app_key" \
  -H "X-Secret-Key: your_secret_key" \
  -d '{"image_url": "https://example.com/test.jpg"}'
```

## 📞 技术支持

### 获取帮助
1. 查看 `INSTALL.md` 详细安装说明
2. 查看 `SECURITY_GUIDE.md` 安全配置指南
3. 查看 `API_DOCS.md` API接口文档
4. 使用在线API测试工具调试

### 联系方式
- 技术文档：查看项目文档
- 在线支持：访问管理后台
- 问题反馈：提交技术支持请求

---

## 🎉 部署成功检查清单

- [ ] 服务正常启动（`./deploy.sh status`）
- [ ] 健康检查通过（`curl http://localhost:8080/health`）
- [ ] 管理后台可访问
- [ ] API测试工具可用
- [ ] 默认管理员密码已修改
- [ ] 防火墙已配置
- [ ] 日志正常记录
- [ ] 安全检查通过（`./security_check.sh`）

**🎯 恭喜！您的拍照搜题API服务已成功部署上线！**
