#!/bin/bash

# 环境变量配置检查脚本
# 使用方法: ./check_env.sh

echo "🔍 检查环境变量配置..."
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查结果统计
PASS_COUNT=0
WARN_COUNT=0
FAIL_COUNT=0

# 检查函数
check_pass() {
    echo -e "${GREEN}✓ PASS${NC}: $1"
    ((PASS_COUNT++))
}

check_warn() {
    echo -e "${YELLOW}⚠ WARN${NC}: $1"
    ((WARN_COUNT++))
}

check_fail() {
    echo -e "${RED}✗ FAIL${NC}: $1"
    ((FAIL_COUNT++))
}

check_info() {
    echo -e "${BLUE}ℹ INFO${NC}: $1"
}

# 检查.env文件是否存在
if [ ! -f ".env" ]; then
    check_fail ".env 文件不存在"
    echo ""
    echo "请创建 .env 文件："
    echo "cp .env.production .env"
    echo "vim .env"
    exit 1
fi

# 加载环境变量
set -a
source .env 2>/dev/null
set +a

echo "1. 检查数据库配置..."
echo "--------------------------------"

# 检查MySQL密码
if [ -n "$MYSQL_PASSWORD" ]; then
    if [ ${#MYSQL_PASSWORD} -ge 8 ]; then
        check_pass "MySQL密码已设置且长度合适"
    else
        check_warn "MySQL密码过短，建议至少8位"
    fi
else
    check_fail "MySQL密码未设置 (MYSQL_PASSWORD)"
fi

echo ""
echo "2. 检查Redis配置..."
echo "--------------------------------"

# 检查Redis密码
if [ -n "$REDIS_PASSWORD" ]; then
    if [ ${#REDIS_PASSWORD} -ge 8 ]; then
        check_pass "Redis密码已设置且长度合适"
    else
        check_warn "Redis密码过短，建议至少8位"
    fi
else
    check_fail "Redis密码未设置 (REDIS_PASSWORD)"
fi

echo ""
echo "3. 检查短信服务配置..."
echo "--------------------------------"

# 检查短信服务配置
if [ -n "$SMS_ACCESS_KEY_ID" ]; then
    check_pass "短信服务Access Key ID已设置"
else
    check_fail "短信服务Access Key ID未设置 (SMS_ACCESS_KEY_ID)"
fi

if [ -n "$SMS_ACCESS_KEY_SECRET" ]; then
    check_pass "短信服务Access Key Secret已设置"
else
    check_fail "短信服务Access Key Secret未设置 (SMS_ACCESS_KEY_SECRET)"
fi

echo ""
echo "4. 检查AI模型配置..."
echo "--------------------------------"

# 检查Qwen API密钥
if [ -n "$QWEN_API_KEY" ]; then
    if [ "$QWEN_API_KEY" = "your_qwen_api_key_here" ]; then
        check_fail "Qwen API密钥未修改，仍为默认值"
    else
        check_pass "Qwen API密钥已设置"
    fi
else
    check_fail "Qwen API密钥未设置 (QWEN_API_KEY)"
fi

# 检查DeepSeek API密钥
if [ -n "$DEEPSEEK_API_KEY" ]; then
    if [ "$DEEPSEEK_API_KEY" = "your_deepseek_api_key_here" ]; then
        check_fail "DeepSeek API密钥未修改，仍为默认值"
    elif [[ "$DEEPSEEK_API_KEY" == sk-* ]]; then
        check_pass "DeepSeek API密钥已设置且格式正确"
    else
        check_warn "DeepSeek API密钥格式可能不正确（应以sk-开头）"
    fi
else
    check_fail "DeepSeek API密钥未设置 (DEEPSEEK_API_KEY)"
fi

echo ""
echo "5. 检查安全配置..."
echo "--------------------------------"

# 检查JWT密钥
if [ -n "$JWT_SECRET" ]; then
    if [ "$JWT_SECRET" = "your_jwt_secret_key_here" ]; then
        check_fail "JWT密钥未修改，仍为默认值"
    elif [ ${#JWT_SECRET} -ge 32 ]; then
        check_pass "JWT密钥已设置且长度合适"
    else
        check_warn "JWT密钥过短，建议至少32位"
    fi
else
    check_fail "JWT密钥未设置 (JWT_SECRET)"
fi

# 检查管理员密码
if [ -n "$ADMIN_DEFAULT_PASSWORD" ]; then
    if [ "$ADMIN_DEFAULT_PASSWORD" = "your_secure_admin_password_here" ]; then
        check_fail "管理员密码未修改，仍为默认值"
    elif [ ${#ADMIN_DEFAULT_PASSWORD} -ge 8 ]; then
        check_pass "管理员密码已设置且长度合适"
    else
        check_warn "管理员密码过短，建议至少8位"
    fi
else
    check_fail "管理员密码未设置 (ADMIN_DEFAULT_PASSWORD)"
fi

echo ""
echo "6. 检查可选配置..."
echo "--------------------------------"

# 检查配置文件路径
if [ -n "$CONFIG_FILE" ]; then
    if [ -f "$CONFIG_FILE" ]; then
        check_pass "配置文件路径正确: $CONFIG_FILE"
    else
        check_fail "配置文件不存在: $CONFIG_FILE"
    fi
else
    check_info "使用默认配置文件路径"
fi

# 检查服务端口
if [ -n "$SERVER_PORT" ]; then
    if [[ "$SERVER_PORT" =~ ^[0-9]+$ ]] && [ "$SERVER_PORT" -ge 1024 ] && [ "$SERVER_PORT" -le 65535 ]; then
        check_pass "服务端口设置正确: $SERVER_PORT"
    else
        check_warn "服务端口设置可能不正确: $SERVER_PORT"
    fi
else
    check_info "使用默认服务端口: 8080"
fi

# 检查日志级别
if [ -n "$LOG_LEVEL" ]; then
    case "$LOG_LEVEL" in
        debug|info|warn|error)
            check_pass "日志级别设置正确: $LOG_LEVEL"
            ;;
        *)
            check_warn "日志级别设置可能不正确: $LOG_LEVEL"
            ;;
    esac
else
    check_info "使用默认日志级别"
fi

echo ""
echo "7. 生成配置建议..."
echo "--------------------------------"

# 生成JWT密钥建议
if [ -z "$JWT_SECRET" ] || [ "$JWT_SECRET" = "your_jwt_secret_key_here" ]; then
    JWT_SUGGESTION=$(openssl rand -base64 32 2>/dev/null || echo "$(date +%s)_$(whoami)_$(hostname)" | sha256sum | cut -d' ' -f1)
    check_info "建议的JWT密钥: $JWT_SUGGESTION"
fi

# 生成强密码建议
if [ -z "$ADMIN_DEFAULT_PASSWORD" ] || [ "$ADMIN_DEFAULT_PASSWORD" = "your_secure_admin_password_here" ]; then
    PASSWORD_SUGGESTION=$(openssl rand -base64 12 2>/dev/null | tr -d "=+/" | cut -c1-12)
    check_info "建议的管理员密码: ${PASSWORD_SUGGESTION}@2024"
fi

echo ""
echo "=================================="
echo "🔍 环境变量检查完成"
echo "=================================="
echo -e "通过: ${GREEN}$PASS_COUNT${NC}"
echo -e "警告: ${YELLOW}$WARN_COUNT${NC}"
echo -e "失败: ${RED}$FAIL_COUNT${NC}"
echo ""

if [ "$FAIL_COUNT" -gt 0 ]; then
    echo -e "${RED}❌ 发现严重配置问题，请修复后再部署！${NC}"
    echo ""
    echo "修复步骤："
    echo "1. 编辑环境变量文件: vim .env"
    echo "2. 修复上述失败项目"
    echo "3. 重新运行检查: ./check_env.sh"
    exit 1
elif [ "$WARN_COUNT" -gt 0 ]; then
    echo -e "${YELLOW}⚠️  发现配置警告，建议优化后部署${NC}"
    echo ""
    echo "建议操作："
    echo "1. 查看上述警告项目"
    echo "2. 根据建议优化配置"
    echo "3. 重新运行检查确认"
    exit 2
else
    echo -e "${GREEN}✅ 环境变量配置检查全部通过！${NC}"
    echo ""
    echo "🚀 可以开始部署："
    echo "1. 设置文件权限: chmod +x solve_api deploy.sh security_check.sh"
    echo "2. 运行安全检查: ./security_check.sh"
    echo "3. 启动服务: ./deploy.sh start"
    exit 0
fi
